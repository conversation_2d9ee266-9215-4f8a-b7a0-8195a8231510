import { ServiceConstructorData, Model, QuerySVOptions } from '@services';
import { useQuery, useMutation } from '@tanstack/vue-query';
import {
  APISVResponseError,
  SVOutletsData,
  SVTopUp,
  SVTopUpPayload,
  SVBonusRatePayload,
  SVTransactionHistoryData,
  SVRecentOutletsData,
  SVOutlet,
  SVPaymentPayload,
  SVPaymentResult,
  SVRateRewards,
  SVTopUpDetails,
} from '@types';

const modelConfig: ServiceConstructorData = {
  path: '',
  service: 'SV',
};

export class VouchersModel extends Model {
  static outlets() {
    return this.api.get<SVOutletsData>({
      url: '/outlet',
    });
  }

  static topUp(data: SVTopUpPayload) {
    return this.api.post<SVTopUp>({
      url: '/topup',
      data,
    });
  }

  static topUpDetail(id: string) {
    return this.api.get<SVTopUpDetails>({
      url: '/topup/reference',
      params: { id },
    });
  }

  static checkPromo(code: string) {
    return this.api.post<SVRateRewards>({
      url: '/topup/check-promocode',
      data: { code },
    });
  }

  static bonusRate(data: SVBonusRatePayload) {
    return this.api.get<SVRateRewards>({
      url: '/topup/bonus-rate',
      params: data,
    });
  }

  static history(data: ComputedRef<unknown>) {
    return this.api.get<SVTransactionHistoryData>({
      url: '/history',
      params: data.value,
    });
  }

  static recentOutlet() {
    return this.api.get<SVRecentOutletsData>({
      url: '/payment/recent-outlet',
    });
  }

  static paymentScan(qr_code: string) {
    return this.api.post<SVOutlet>({
      url: '/payment/scan',
      data: { qr_code },
    });
  }

  static payment(data: SVPaymentPayload) {
    return this.api.post<SVPaymentResult>({
      url: '/payment',
      data,
    });
  }
}

VouchersModel.setup(modelConfig);

export const SV_OUTLETS_QUERY = 'SV_OUTLETS_QUERY';
export function useSVOutletsQuery(options: Partial<QuerySVOptions<SVOutletsData>> = {}) {
  return useQuery<SVOutletsData, APISVResponseError>({
    queryKey: [SV_OUTLETS_QUERY],
    queryFn: () => VouchersModel.outlets().then((r) => r.data),
    ...options,
  });
}

export const SV_TOPUP_MUTATION = 'SV_TOPUP_MUTATION';
export function useSVTopUpMutation() {
  return useMutation<SVTopUp, APISVResponseError, SVTopUpPayload>({
    mutationKey: [SV_TOPUP_MUTATION],
    mutationFn: (data) => VouchersModel.topUp(data).then((r) => r.data),
  });
}

export const SV_TOPUP_DETAIL_QUERY = 'SV_TOPUP_DETAIL_QUERY';
export function useSVTopUpDetailQuery(
  id: string,
  options: Partial<QuerySVOptions<SVTopUpDetails>> = {},
) {
  return useQuery<SVTopUpDetails, APISVResponseError>({
    queryKey: [SV_TOPUP_DETAIL_QUERY, id],
    queryFn: () => VouchersModel.topUpDetail(id).then((r) => r.data),
    ...options,
  });
}

export const SV_CHECK_PROMO_MUTATION = 'SV_CHECK_PROMO_MUTATION';
export function useSVCheckPromoMutation() {
  return useMutation<SVRateRewards, APISVResponseError, string>({
    mutationKey: [SV_CHECK_PROMO_MUTATION],
    mutationFn: (code) => VouchersModel.checkPromo(code).then((r) => r.data),
  });
}

export const SV_BONUS_RATE_QUERY = 'SV_BONUS_RATE_QUERY';
export function useSVBonusRateQuery(
  data: ComputedRef<SVBonusRatePayload>,
  options: Partial<QuerySVOptions<SVRateRewards>> = {},
) {
  return useQuery<SVRateRewards, APISVResponseError>({
    queryKey: [SV_BONUS_RATE_QUERY, data],
    queryFn: () => VouchersModel.bonusRate(data.value).then((r) => r.data),
    ...options,
  });
}

export const SV_HISTORY_QUERY = 'SV_HISTORY_QUERY';
export function useSVHistoryQuery(
  data: ComputedRef<unknown>,
  options: Partial<QuerySVOptions<SVTransactionHistoryData>> = {},
) {
  return useQuery<SVTransactionHistoryData, APISVResponseError>({
    queryKey: [SV_HISTORY_QUERY, data],
    queryFn: () => VouchersModel.history(data).then((r) => r.data),
    ...options,
  });
}

export const SV_RECENT_OUTLET_QUERY = 'SV_RECENT_OUTLET_QUERY';
export function useSVRecentOutletQuery(options: Partial<QuerySVOptions<SVRecentOutletsData>> = {}) {
  return useQuery<SVRecentOutletsData, APISVResponseError>({
    queryKey: [SV_RECENT_OUTLET_QUERY],
    queryFn: () => VouchersModel.recentOutlet().then((r) => r.data),
    ...options,
  });
}

export const SV_PAYMENT_SCAN_MUTATION = 'SV_PAYMENT_SCAN_MUTATION';
export function useSVPaymentScanMutation() {
  return useMutation<SVOutlet, APISVResponseError, string>({
    mutationKey: [SV_PAYMENT_SCAN_MUTATION],
    mutationFn: (qr_code) => VouchersModel.paymentScan(qr_code).then((r) => r.data),
  });
}

export const SV_PAYMENT_MUTATION = 'SV_PAYMENT_MUTATION';
export function useSVPaymentMutation() {
  return useMutation<SVPaymentResult, APISVResponseError, SVPaymentPayload>({
    mutationKey: [SV_PAYMENT_MUTATION],
    mutationFn: (data) => VouchersModel.payment(data).then((r) => r.data),
  });
}
