import { TGeocontrolState } from '@types';
import { Map } from 'vue3-mapbox';
import { GeolocateControl } from 'maplibre-gl';

interface MapState {
  mapInstance: ComputedRef<Map>;
  geoInstance: ComputedRef<GeolocateControl>;
  geoState: TGeocontrolState;
  isMapLoaded: boolean;
  isLoading: boolean;
}

export const useMapStore = defineStore('map', {
  state: (): MapState => ({
    mapInstance: {} as ComputedRef<Map>,
    geoInstance: {} as ComputedRef<GeolocateControl>,
    geoState: 'UNAVAILABLE',
    isMapLoaded: false,
    isLoading: true,
  }),
  getters: {},
  actions: {
    setMapInstance(map: Map): void {
      this.mapInstance = map;
    },

    setGeoInstance(geo: GeolocateControl): void {
      this.geoInstance = geo;
    },

    setGeoState(state: TGeocontrolState): void {
      this.geoState = state;
    },

    setIsMapLoaded(loaded: boolean): void {
      this.isMapLoaded = loaded;
    },

    setIsLoading(loading: boolean): void {
      this.isLoading = loading;
    },
  },
});
