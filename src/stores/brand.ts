import { ActivatingTimeMission, BrandAction, GlobalBrandAction, TimeMission } from '@types';

interface BAState {
  brand_actions: BrandAction[];
  newBrandActions: BrandAction[];
  user_brand_actions: BrandAction[];
  timedMissions: TimeMission[];
  pendingMissions: ActivatingTimeMission[];
  timeMissionData: ActivatingTimeMission | null;
  skip_mission_price: number;
  timed_mission_cooldown: string;
}

export const useBAStore = defineStore('brand_actions', {
  state: (): BAState => ({
    brand_actions: [],
    newBrandActions: [],
    user_brand_actions: [],
    timedMissions: [],
    pendingMissions: [],
    timeMissionData: null,
    skip_mission_price: 0,
    timed_mission_cooldown: '',
  }),
  getters: {},
  actions: {
    setOfferWallData(data: GlobalBrandAction): void {
      Object.assign(this, {
        skip_mission_price: data.skip_mission_price,
        timed_mission_cooldown: data.timed_mission_cooldown,
        timedMissions: data.timed_missions ?? [],
      });

      this.mapBrandActions(data);

      const hasUnclaimedMissions = data.unclaimed_timed_missions?.length > 0;
      const timeMissionInput: GlobalBrandAction = hasUnclaimedMissions
        ? { ...data, activating_timed_mission: data.unclaimed_timed_missions[0]! }
        : data;

      this.mapTimeMissionData(timeMissionInput);
      this.mapPendingTimeMission(data.pendingMissions ?? []);
    },

    mapBrandActions(data: GlobalBrandAction): void {
      const featuredMap = new Map(
        data.featured_brand_actions?.map((item) => [item.brand_action, item]) ?? [],
      );
      const bonusMap = new Map(
        data.bonus_brand_actions?.map((item) => [item.brand_action, item]) ?? [],
      );

      const brandActionsMap = new Map<string, BrandAction>();
      const filteredBrandActions: BrandAction[] = [];
      const newBrandActions: BrandAction[] = [];

      data.brand_actions.forEach((item) => {
        if (!item.show_on_list) return;

        const enhancedItem = {
          ...item,
          featured: featuredMap.get(item._id),
          bonus: bonusMap.get(item._id),
        } as BrandAction;

        brandActionsMap.set(item.unique_id, enhancedItem);
        filteredBrandActions.push(enhancedItem);

        if (item.can_perform) newBrandActions.push({ ...enhancedItem, status: 'new' });
      });

      this.brand_actions = filteredBrandActions;
      this.newBrandActions = newBrandActions;

      this.user_brand_actions = data.user_brand_actions.map((uba) => {
        const ba = brandActionsMap.get(uba.ba_unique_id);
        return {
          ...ba,
          ...uba,
          _id: uba._id,
          reward: ba?.reward,
          user_reward: uba?.reward,
        } as BrandAction;
      });
    },

    mapTimeMissionData(data: GlobalBrandAction): void {
      const { activating_timed_mission } = data;

      if (!activating_timed_mission) {
        this.timeMissionData = this.timeMissionData
          ? { ...this.timeMissionData, is_active: false }
          : null;
        return;
      }

      const brandActionMap = new Map(data.brand_actions.map((ba) => [ba.unique_id, ba]));
      const timedMissionMap = new Map(
        data.timed_missions.map((mission) => [mission.unique_id, mission]),
      );

      const brandAction = brandActionMap.get(activating_timed_mission.ba_unique_id) as BrandAction;
      const mission = timedMissionMap.get(
        activating_timed_mission.mission_unique_id,
      ) as TimeMission;
      const nextMission = mission?.next_mission_unique_id
        ? timedMissionMap.get(mission.next_mission_unique_id)
        : undefined;

      this.timeMissionData = {
        ...activating_timed_mission,
        is_active: true,
        brandAction,
        mission,
        unclaimed_timed_missions: data.unclaimed_timed_missions,
        ...(nextMission && { nextMission }),
      };
    },

    mapPendingTimeMission(data: ActivatingTimeMission[]): void {
      if (!data.length) {
        this.pendingMissions = [];
        return;
      }

      const timedMissionsMap = new Map(this.timedMissions.map((item) => [item.unique_id, item]));
      const userBrandActionsMap = new Map(
        this.user_brand_actions.map((ba) => [ba.ba_unique_id, ba]),
      );

      this.pendingMissions = data.map((el) => ({
        ...el,
        mission: timedMissionsMap.get(el.mission_unique_id) as TimeMission,
        brandAction: userBrandActionsMap.get(el.ba_unique_id) as BrandAction,
      }));
    },
  },
});
