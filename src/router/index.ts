import { defineRouter } from '#q-app/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';
import routes from './routes';
import { usePageTracker } from '@composables';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

const TRACKED_PATHS = [
  'vote',
  'geo',
  'students',
  'tt',
  'fb',
  'ig',
  'tele',
  'x',
  'web',
  'reen',
  'yuni',
  'jess',
  'playinsg',
  'hall10',
  'hall16',
  'crescenthall',
  'hall8',
  'pioneerhall',
  'hall12',
  'hall7',
  'raffleshall',
  'hall15',
  'email',
  'hall14',
  'hall1',
  'saracahall',
  'hunt',
  'sonar',

  // HTM 1M
  'ooh',
  'oohpsdAJN',
  'oohpsdBB',
  'oohpsdCBR',
  'oohpsdDKT',
  'oohpsdFR',
  'oohpsdHV',
  'oohpsdKTB',
  'oohpsdLS',
  'oohpsdMPS',
  'oohpsdMSL',
  'oohpsdON',
  'oohpsdTS',
  'oohpsdTB',
  'oohpsdYT',
  'ooh4s',
  'ooh6s',
  'll',
  'oohwink',
  'pp',
  'ppcounter',
  'ppstandee',
  'ppcards',
  'sqfb',
  'sqig',
  'sqtt',
  'sqtele',
  'pr',
  'partipost',
  'gold',
  'silver',

  // sentosa
  'sentosa',
  'ppsen',
  'pptentsen',
  'ppscratchsen',
  'ppcardsen',
  'prsen',
  'crystal',
  'sentosasilver',
] as const;

export default defineRouter(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
      ? createWebHistory
      : createWebHashHistory;

  const router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  router.beforeEach((to, _from, next) => {
    const { track } = usePageTracker();

    const path = (to.redirectedFrom?.path ?? to.path).replace('/', '');

    if (TRACKED_PATHS.includes(path as (typeof TRACKED_PATHS)[number])) {
      track({
        id: 'custom_path',
        action: 'visit_link',
        data: { path },
      });
    }

    if (to.path !== '/') return next('/');

    return next();
  });

  return router;
});
