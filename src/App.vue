<script lang="ts" setup>
import { Loading } from '@components';
import { useFetchQueries } from '@composables';
import { usePageTracker } from '@composables';

const { fetched } = useFetchQueries(true);
const { setupGlobalLinkTracking } = usePageTracker();

const isMobile = computed(() => (window as any).mobileCheck());

const loading = ref(true);

onMounted(() => {
  setupGlobalLinkTracking();
});
</script>

<template>
  <template v-if="isMobile">
    <q-dialog
      v-model="loading"
      fullscreen
      position="standard"
      transition-show="none"
      transition-hide="fade"
    >
      <Loading :fetched="fetched" @success="loading = false" />
    </q-dialog>
  </template>

  <RouterView />
</template>
