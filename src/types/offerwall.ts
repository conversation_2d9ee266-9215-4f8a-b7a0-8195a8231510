export type BrandActionStatus = 'new' | 'pending' | 'claimed' | 'verified' | 'rejected';

export type BrandActionType =
  | 'contest'
  | 'receipt_verification'
  | 'enter_promo_code'
  | 'promo_code'
  | 'sqkii_voucher'
  | 'enter_barcode'
  | 'visit_web'
  | 'survey'
  | 'verify_mobile_number'
  | 'client_verify'
  | 'sharing'
  | 'open_external_link'
  | 'read_spf_message'
  | 'location_based'
  | 'spf_quiz'
  | 'spf_sharing'
  | 'sync_account_api'
  | 'etiqa_insurance'
  | 'lendlease_sync_account'
  | 'tiger_broker_sync_account'
  | 'tiger_broker_register_boss_card'
  | 'tiger_broker_deposit'
  | 'tada_ride'
  | 'use_sqkii_voucher'
  | 'open_sentosa_app'
  | 'scan_qrcode'
  | 'head_over_sentosa';

export type ComponentId =
  | 'gold_coin_hint_cards_front_outfit'
  | 'silver_coin_circle_shrinks_logo'
  | 'silver_coin_circle_shrinks_character'
  | 'gold_coin_location_eliminations_logo'
  | 'gold_coin_location_eliminations_character'
  | 'gold_coin_text_hint_mascot'
  | 'gold_coin_text_hint_reveals_back_logo'
  | 'gold_coin_hint_shop_kv'
  | 'brand_actions_offerwall_kv'
  | 'ensuring_fairness_kv'
  | 'menu_htm_logo'
  | 'loading_screen_kv'
  | 'welcome_screen_htm_logo'
  | 'safety_hints_pop_up_character'
  | 'safety_hints_pop_up_flag'
  | 'pre_launch_landing_htm_van'
  | 'pre_launch_landing_htm_logo'
  | 'main_game_map_border';

export interface Milestones {
  _id: string;
  season: string;
  unique_id: string;
  created_at: string;
  reward: number;
  updated_at: string;
  required: number;
  completed: boolean;
  claimed: boolean;
}

export interface Bonus {
  bonus: number;
  brand_action: string;
  created_at: string;
  disabled: boolean;
  end_at: string;
  start_at: string;
  _id: string;
}

export interface Featured {
  brand_action: string;
  created_at: string;
  end_at: string;
  logo: string;
  slot: string;
  start_at: string;
  title: string;
  updated_at: string;
  _id: string;
}

export interface BrandAction {
  brand_unique_id: string;
  _id: string;
  brand: {
    _id: string;
    name: string;
  };
  brand_action: string;
  button_copy: string;
  header_copy: string;
  outlet_copy?: string;
  outlet_popup_header?: string;
  title: string;
  reward?: UserRewards;
  display_reward: string;
  display_reward_first_time: string;
  user_reward?: UserRewards;
  seen?: boolean;
  user_ba_id?: string;
  is_first_time?: boolean;
  claimed_at?: string;
  created_at?: string;
  verified_at?: string;
  updated_at?: string;
  bonus?: Bonus;
  bonus_end_at?: string;
  release_date?: string;
  close_date?: string;
  brand_id: string;
  instruction: string;
  instructions: string[];
  verify_type?: 'email' | 'mobile_number';
  rejected_at?: string;
  reject_reason?: string;
  description?: string;
  link?: string;
  outlets?: {
    name: string;
    address: string;
    s_n: string;
  }[];
  malls?: {
    name: string;
    address: string;
    s_n: string;
  }[];
  featured?: {
    start_at: string;
    end_at: string;
    slot: string;
    logo: string;
  };
  type: BrandActionType;
  lock_until?: string;
  status: BrandActionStatus;
  data: Record<string, any>;
  metadata: Record<string, any>;
  unique_id: string;
  can_perform: boolean;
  ba_unique_id: string;
  show_on_list: boolean;
  group_id: string;
}

export interface TimeMissionProgress {
  current: number;
  prefix_unit: string;
  required: number;
  suffix_unit: string;
  show_unit_on_current: boolean;
}

export interface TimeMission {
  brand_unique_id: string;
  description: string;
  duration: number;
  prev_mission_unique_id: string;
  next_mission_unique_id: string;
  reward: number;
  unique_id: string;
  type: BrandActionType;
  ba_group_id: string;
  _id: string;
}

export interface ActivatingTimeMission {
  _id: string;
  user: string;
  mission_unique_id: string;
  date: string;
  type: BrandActionType;
  brand_unique_id: string;
  ba_group_id: string;
  mission_group_id: string;
  ba_unique_id: string;
  status: BrandActionStatus;
  start_at: string;
  expired_at: string;
  progress: TimeMissionProgress;
  updated_at: string;
  brandAction: BrandAction;
  mission: TimeMission;
  unclaimed_timed_missions: ActivatingTimeMission[];
  nextMission?: TimeMission;
  data?: Record<string, any>;
  is_active?: boolean;
}

export interface GlobalBrandAction {
  brand_actions: BrandAction[];
  user_brand_actions: BrandAction[];
  milestones: Milestones[];
  featured_brand_actions: Featured[];
  bonus_brand_actions: Bonus[];
  activating_timed_mission: ActivatingTimeMission;
  timed_missions: TimeMission[];
  unclaimed_timed_missions: ActivatingTimeMission[];
  skip_mission_price: number;
  timed_mission_cooldown: string;
  pendingMissions?: ActivatingTimeMission[];
}

export interface BrandActionHook {
  handleBack: () => void;
  handleAction: (item: BrandAction) => Promise<void>;
  claimAll: () => Promise<void>;
  showStatus: (item: BrandAction) => void;
  checkWithFeatured: (item: BrandAction) => boolean;
  checkWithBonus: (item: BrandAction) => boolean | undefined;
  calculateReward: (item: BrandAction) => string | number;
  locked_item: (item: BrandAction) => BrandAction | undefined;
  hasMultiplier: (item: BrandAction) => boolean | undefined;
}

export interface UserRewards {
  beacon: number;
  crystal: number;
}

export interface BrandIcon extends BrandAction {
  location: {
    lat: number;
    lng: number;
  };
  display: {
    brand_name: string;
    name: string;
    address: string;
    brand_icon: string;
    prefer_layer: string;
    sv_client?: string;
    opening_hours: Record<string, string>;
  };
  brand_unique_id: string;
  unique_id: string;
  opening_hours: Record<string, string>;
  brand_icon: string;
}

export interface UpdateLocationBased {
  updatedData: {
    progress: TimeMissionProgress;
    status: BrandActionStatus;
  };
  updated: boolean;
}
