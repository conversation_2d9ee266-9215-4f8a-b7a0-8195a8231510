import { GamePlugin } from '@core/SeasonManager';
import { ForgotPassword, LockedAccount, Login, Logout, SignUp, SignUpSuccess } from './dialogs';

class GameManagerPlugin extends GamePlugin {
  override getPublicAPI<T>(): T {
    throw new Error('Method not implemented.');
  }
}

export const AuthPlugin = new GameManagerPlugin(
  'AuthPlugin',
  [],
  [
    {
      path: 'logout',
      component: Logout,
      actived: false,
    },
    {
      path: 'login',
      component: Login,
      actived: false,
    },
    {
      path: 'sign_up',
      component: SignUp,
      actived: false,
    },
    {
      path: 'locked_account',
      component: LockedAccount,
      actived: false,
    },
    {
      path: 'signup_success',
      component: SignUpSuccess,
      actived: false,
    },
    {
      path: 'forgot_password',
      component: ForgotPassword,
      actived: false,
    },
  ],
  [],
);
