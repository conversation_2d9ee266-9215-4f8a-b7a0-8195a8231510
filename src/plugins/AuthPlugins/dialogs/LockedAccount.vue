<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';
import { useI18n } from 'vue-i18n';
import { useNow } from '@composables';
import { timeCountDown } from '@utils';

const now = useNow();

const { openDialog, push } = useMicroRoute();
const { t } = useI18n();

type AccountStatus = 'banned' | 'locked';

interface Props {
  lock_until?: string;
  type: AccountStatus;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();

const emits = defineEmits<Emits>();

const countdown = computed((): number => {
  if (!props.lock_until) return 0;

  const lockTime = new Date(props.lock_until).getTime();
  if (isNaN(lockTime)) return 0;

  const remaining = lockTime - now.value;
  return Math.max(0, remaining);
});

const isBanned = computed(() => props.type === 'banned');
const isLocked = computed(() => props.type === 'locked');
const isLoginDisabled = computed(() => countdown.value > 0);

const titleText = computed(() =>
  isBanned.value ? t('LOGIN_ACCOUNTBANNED_TITLE') : t('LOGIN_ACCOUNTLOCKED_TITLE'),
);

const descriptionText = computed(() =>
  isBanned.value ? t('LOGIN_ACCOUNTBANNED_DESC') : t('LOGIN_ACCOUNTLOCKED_DESC'),
);

function handleBackToLogin(): void {
  emits('close');
  openDialog('login');
}

function handleBackToMap(): void {
  emits('close');
  push('/home');
}
</script>

<template>
  <div
    class="fullscreen bg-[#090422] column flex-nowrap items-center text-center justify-center p-[40px]"
  >
    <Icon name="timii_locked_account" :size="164" class="mb-4" />

    <div class="font-bold text-lg mb-4" v-html="titleText" />

    <div class="text-sm mb-6" v-html="descriptionText" />

    <template v-if="isLocked">
      <div class="mb-6">
        <Button track-id="disable-track" :disabled="isLoginDisabled" @click="handleBackToLogin">
          {{ t('LOGIN_ACCOUNTLOCKED_BUTTON') }}
          <span v-if="countdown > 0" class="whitespace-nowrap ml-2">
            ({{ timeCountDown(countdown) }})
          </span>
        </Button>
      </div>

      <div
        class="text-sm underline text-link cursor-pointer"
        v-html="t('LOGIN_ACCOUNTLOCKED_BACKTOMAP')"
        @click="handleBackToMap"
      />
    </template>
  </div>
</template>
