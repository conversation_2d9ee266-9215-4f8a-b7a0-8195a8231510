<script lang="ts" setup>
import { useLogoutMutation } from '@services';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const mutation = useLogoutMutation();

const { t } = useI18n();

const isLoading = computed(() => mutation.isPending.value);

async function handleLogout(): Promise<void> {
  try {
    await mutation.mutateAsync();
    LocalStorage.clear();
    location.reload();
  } catch (error) {
    console.error('Logout failed:', error);
  }
}
</script>

<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        track-id="disable-track"
        shape="square"
        variant="secondary"
        @click="emits('close')"
        :disabled="isLoading"
      >
        <Icon name="arrow-left" :size="14"></Icon>
      </Button>
    </template>

    <template #header>
      <div v-html="t('SETTINGS_LEAVINGPOPUP_HEADING')"></div>
    </template>

    <div class="text-sm text-center mb-5" v-html="t('SETTINGS_LEAVINGPOPUP_DESC')"></div>

    <div class="text-center">
      <Button
        track-id="logout"
        :track-data="{
          target: 'logout_success',
        }"
        :loading="isLoading"
        :label="t('SETTINGS_BUTTON_LOGOUT')"
        @click="handleLogout"
      ></Button>
    </div>
  </Dialog>
</template>
