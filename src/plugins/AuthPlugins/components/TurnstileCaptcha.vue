<script lang="ts" setup>
import VueTurnstile from 'vue-turnstile';

interface Props {
  resetTrigger?: boolean;
}

interface Emits {
  (e: 'success', token: string): void;
  (e: 'error'): void;
  (e: 'expired'): void;
}

const props = withDefaults(defineProps<Props>(), {
  resetTrigger: false,
});

const emits = defineEmits<Emits>();

const turnstileRef = ref<InstanceType<typeof VueTurnstile>>();
const siteKey = process.env.APP_TURNSTILE_SITE_KEY as string;
const token = ref('');

watch(
  () => props.resetTrigger,
  (newValue) => {
    if (newValue && turnstileRef.value) {
      turnstileRef.value.reset();
    }
  },
);

watch(
  token,
  (_token) => {
    if (_token) emits('success', token.value);
  },
  {
    immediate: true,
  },
);

function onError(): void {
  token.value = '';
  emits('error');
}

function onExpired(): void {
  token.value = '';
  emits('expired');
}

function reset(): void {
  token.value = '';
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
}

onBeforeUnmount(() => {
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
});

defineExpose({
  reset,
});
</script>

<template>
  <div class="turnstile-captcha">
    <VueTurnstile
      ref="turnstileRef"
      v-model="token"
      :site-key="siteKey"
      theme="auto"
      size="normal"
      language="en"
      appearance="always"
      @error="onError"
      @expired="onExpired"
    />
  </div>
</template>

<style scoped>
.turnstile-captcha {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}
</style>
