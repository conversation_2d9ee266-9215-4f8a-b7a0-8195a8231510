import { GamePlugin } from '@core/SeasonManager';
import {
  AdventureLogs,
  Announcement,
  CameraPermission,
  ChangeHunterID,
  ChangeHunterIDSuccess,
  DontLikeHunterID,
  ReferralQuestionMark,
  TAC,
  TimelineLogs,
  TimelineRegister,
  TipsTrick,
  UserRewards,
} from './dialogs';
import { EnsuringFairness, FAQ, Menu, Referral, Settings } from './views';

class GameManagerPlugin extends GamePlugin {
  override getPublicAPI<T>(): T {
    throw new Error('Method not implemented.');
  }
}

export const BasePlugin = new GameManagerPlugin(
  'BasePlugin',
  [
    {
      path: 'faq',
      component: FAQ,
    },
    {
      path: 'menu',
      component: Menu,
    },
    {
      path: 'ensuring_fairness',
      component: EnsuringFairness,
    },
    {
      path: 'referral',
      component: Referral,
    },
    {
      path: 'settings',
      component: Settings,
    },
  ],
  [
    {
      path: 'announcement',
      component: Announcement,
      actived: false,
    },
    {
      path: 'adventure_logs',
      component: AdventureLogs,
      actived: false,
    },
    {
      path: 'timeline_logs',
      component: TimelineLogs,
      actived: false,
      fullscreen: true,
      transitionShow: 'slide-left',
      transitionHide: 'slide-right',
      transitionDuration: 500,
    },
    {
      path: 'timeline_register',
      component: TimelineRegister,
      actived: false,
    },
    {
      path: 'tips_trick',
      component: TipsTrick,
      actived: false,
    },
    {
      path: 'tac',
      component: TAC,
      actived: false,
      fullscreen: true,
      transitionShow: 'slide-left',
      transitionHide: 'slide-right',
      transitionDuration: 500,
    },
    {
      path: 'referral_question_mark',
      component: ReferralQuestionMark,
      actived: false,
    },
    {
      path: 'change_hunter_id',
      component: ChangeHunterID,
      actived: false,
    },
    {
      path: 'change_hunter_id_success',
      component: ChangeHunterIDSuccess,
      actived: false,
    },
    {
      path: 'dont_like_hunter_id',
      component: DontLikeHunterID,
      actived: false,
    },
    {
      path: 'user_rewards',
      component: UserRewards,
      actived: false,
      fullscreen: true,
      transitionShow: 'fade',
      transitionHide: 'fade',
    },
    {
      path: 'camera_permission',
      component: CameraPermission,
      actived: false,
    },
  ],
  [],
);
