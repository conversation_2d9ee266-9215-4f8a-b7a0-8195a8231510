<script setup lang="ts">
import { successNotify, getSocials, playSFX } from '@utils';
import { useUserStore } from '@stores';
import { copyToClipboard, Loading, Platform, LocalStorage } from 'quasar';
import { debounce } from 'lodash';
import { useGlobalData, usePageTracker } from '@composables';
import { TUserLang, UserSetting, UserSettingType } from '@types';
import { useChangeLanguageMutation, useUpdateUserSettingMutation } from '@services';
import { STORAGE_KEYS } from '@enums';

const DEFAULT_VOLUME = 100;
const SOUND_UPDATE_DELAY = 500;
const MIN_VOLUME = 0;
const MAX_VOLUME = 100;

const storeUser = useUserStore();
const userSettingMutation = useUpdateUserSettingMutation();
const changeLangMutation = useChangeLanguageMutation();

const { track } = usePageTracker();
const { push, openDialog } = useMicroRoute();
const { user, gameFeatures, gameSettings, currentCountryCode } = storeToRefs(storeUser);
const { t, locale } = useI18n();
const { appLanguageCode, triggerMidGameSurvey, triggerEndGameSurvey, isUserLogged } =
  useGlobalData();

const lang = ref<TUserLang>(user.value?.lang || appLanguageCode.value);
const expand = ref(false);

const BGM = ref(getInitialVolume('background_music'));
const SFX = ref(getInitialVolume('sound_effect'));

const languageData = computed(() => {
  const supportedLanguages = gameSettings.value?.supported_languages;
  if (!supportedLanguages?.length) return {};

  return supportedLanguages.reduce(
    (acc, langCode) => {
      acc[langCode] = {
        label: langCode === 'en' ? t('LANG_EN') : t('LANG_TRANSLATE'),
        value: langCode as TUserLang,
      };
      return acc;
    },
    {} as Record<string, { label: string; value: TUserLang }>,
  );
});

const availableLanguages = computed(() =>
  Object.values(languageData.value).filter((l) => l.value !== user.value?.lang),
);

const currentLanguageLabel = computed(() =>
  user.value?.lang ? languageData.value[user.value.lang]?.label : '',
);

const hasMultipleLanguages = computed(
  () => (gameSettings.value?.supported_languages?.length ?? 0) >= 2,
);

const showSurvey = computed(() => triggerMidGameSurvey.value || triggerEndGameSurvey.value);

const surveyType = computed(() => (triggerEndGameSurvey.value ? 'end_survey' : 'mid_survey'));

const gpsHelpUrl = computed(() =>
  Platform.is.ios
    ? 'https://support.apple.com/en-sg/HT207092'
    : 'https://support.google.com/accounts/answer/6179507?hl=en&ref_topic=7189122',
);

function getInitialVolume(settingKey: keyof UserSetting): number {
  const setting = user.value?.setting?.[settingKey];
  return Number(setting) > -1 ? Number(setting) : DEFAULT_VOLUME;
}

async function handleCopyClipboard(): Promise<void> {
  if (!user.value?.hunter_id) return;

  await copyToClipboard(user.value.hunter_id);
  successNotify({
    message: t('COPIED_HUNTER_ID'),
    timeout: 3000,
  });
}

function updateSoundSetting(type: UserSettingType, value: number): void {
  playSFX('button');
  track({
    id: 'settings',
    action: 'click',
    data: {
      target: type,
      value,
    },
  });
  userSettingMutation.mutate({ type, value });
}

function updateUserSetting(settingKey: keyof UserSetting, value: number): void {
  if (!user.value) return;

  storeUser.updateUser({
    setting: {
      ...user.value.setting,
      [settingKey]: value,
    } as UserSetting,
  });
}

function handleChangeLanguage(selectedLang: TUserLang): void {
  if (selectedLang === lang.value || changeLangMutation.isPending.value) return;

  changeLangMutation.mutate(selectedLang, {
    onSuccess: () => {
      Loading.show();
      LocalStorage.set(STORAGE_KEYS.LANGUAGE, selectedLang);
      storeUser.updateUser({ lang: selectedLang });
      locale.value = selectedLang;
      lang.value = selectedLang;
      track({
        id: 'settings',
        action: 'click',
        data: {
          target: 'change_language',
          lang: selectedLang,
        },
      });
      expand.value = false;
    },
    onError: (error) => {
      console.error('Error changing language:', error);
    },
    onSettled: () => {
      Loading.hide();
    },
  });
}

const debouncedSoundUpdate = debounce(updateSoundSetting, SOUND_UPDATE_DELAY);

function goToDialog(path: string): void {
  track({
    id: 'settings',
    action: 'click',
    data: {
      target: path,
    },
  });
  openDialog(path);
}

watch(BGM, (value) => {
  updateUserSetting('background_music', value);
  debouncedSoundUpdate('background_music', value);
});

watch(SFX, (value) => {
  updateUserSetting('sound_effect', value);
  debouncedSoundUpdate('sound_effect', value);
});
</script>

<template>
  <div class="fullscreen setting">
    <div class="fixed top-0 left-0 z-10 flex items-center justify-center w-full h-20">
      <Button
        track-id="disable-track"
        class="absolute left-3"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-extrabold" v-html="t('SETTINGS_HEADING')" />
    </div>

    <div class="flex flex-col items-center justify-start p-5 setting-content flex-nowrap">
      <div
        class="mb-2 text-sm text-center"
        v-html="isUserLogged ? t('SETTINGS_LOGGED_AS') : t('SETTINGS_NOTLOGGED')"
      />

      <div
        class="mb-1 text-lg font-bold text-center cursor-pointer"
        @click="handleCopyClipboard"
        v-html="
          t(isUserLogged ? 'SETTINGS_HUNTERNAME' : 'SETTINGS_NOTLOGGEDIN_GUESTNAME', {
            HUNTER_ID: user?.hunter_id,
          })
        "
      />

      <template v-if="isUserLogged && gameFeatures?.change_hunter_id">
        <div class="mt-1 mb-3 text-sm font-normal text-center" v-html="t('SETTINGS_LOGGEDIN_3')" />
        <Button
          track-id="disable-track"
          class="!w-[200px]"
          style="flex: 0 0 48px"
          :label="t('SETTINGS_BUTTON_CHANGEHUNTERID')"
          @click="goToDialog('change_hunter_id')"
        />
      </template>

      <template v-if="!isUserLogged">
        <div class="flex flex-col gap-3 mt-3">
          <Button
            track-id="disable-track"
            class="!w-[280px]"
            :label="t('SETTINGS_BUTTON_SIGNUPFORANACCOUNT')"
            @click="goToDialog('sign_up')"
          />
          <Button
            track-id="disable-track"
            variant="purple"
            class="!w-[280px]"
            :label="t('SETTINGS_BUTTON_IHAVEANACCOUNT')"
            @click="goToDialog('login')"
          />
        </div>
      </template>

      <div class="mt-5 w-[280px]">
        <div class="mb-2 font-bold" v-html="t('SETTINGS_BGM')" />
        <q-slider v-model="BGM" class="mb-3 slider-bgm" :min="MIN_VOLUME" :max="MAX_VOLUME" />

        <div class="mb-2 line" />

        <div class="mb-2 font-bold" v-html="t('SETTINGS_SFX')" />
        <q-slider v-model="SFX" class="mb-3 slider-sfx" :min="MIN_VOLUME" :max="MAX_VOLUME" />
      </div>

      <div
        v-if="currentCountryCode === 'VN'"
        class="px-5 text-sm text-center"
        v-html="t('SETTINGS_VN_URL')"
      />

      <Expansion
        v-if="hasMultipleLanguages"
        v-model="expand"
        group="language"
        class="language mt-5 !w-[250px]"
        :class="{ 'pointer-events-none': !hasMultipleLanguages }"
      >
        <template #header>
          <div class="flex-1 column flex-nowrap">
            <div
              class="text-[8px] transition-all"
              :class="{ 'mb-2': expand }"
              v-html="t('SETTING_LANGUAGE')"
            />
            <div
              class="text-sm"
              :class="{ 'text-[#00e0ff] font-bold': expand }"
              v-html="t(currentLanguageLabel || 'SETTING_LANGUAGE')"
            />
          </div>
        </template>

        <q-card style="background: transparent">
          <div
            v-for="language in availableLanguages"
            :key="language.value"
            class="pt-2 mx-4 mb-3 text-sm cursor-pointer"
            style="border-top: 0.5px solid #00e0ff"
            v-html="t(language.label)"
            @click="handleChangeLanguage(language.value)"
          />
        </q-card>
      </Expansion>

      <template v-if="showSurvey">
        <div class="flex flex-col items-center justify-center mt-5">
          <div
            class="px-10 mb-3 text-sm text-center"
            v-html="t('SETTINGS_NOTLOGGEDIN_COMPLETESURVEY')"
          />
          <div class="relative">
            <Icon class="mx-auto" name="timii" :size="45" />
            <Button
              track-id="disable-track"
              :label="t('SETTINGS_BUTTON_STARTSURVEY')"
              class="!w-[200px]"
              @click="goToDialog(surveyType)"
            />
          </div>
        </div>
      </template>

      <div class="mt-5 text-center">
        <div class="px-10 mb-5 text-sm" v-html="t('SETTINGS_SOCIALMEDIA')" />
        <div class="flex items-center justify-center gap-4">
          <a
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>

      <div v-if="isUserLogged" class="mt-5">
        <Button
          track-id="disable-track"
          class="!w-[200px]"
          variant="purple"
          :label="t('SETTINGS_BUTTON_LOGOUT')"
          @click="goToDialog('logout')"
        />
      </div>

      <div class="flex flex-col items-center justify-center gap-2 mt-5">
        <a
          :href="gpsHelpUrl"
          target="_blank"
          rel="noopener noreferrer"
          class="text-sm underline text-link"
          v-html="t('SETTINGS_GPS')"
          v-tracker="{
            id: 'settings',
            action: 'click',
            data: {
              target: 'gps_help',
              url: gpsHelpUrl,
            },
          }"
        />
        <div class="flex items-center justify-center gap-5">
          <div
            class="text-sm underline cursor-pointer text-link"
            v-html="t('SETTINGS_TACS')"
            @click="goToDialog('tac')"
          />
          <div
            class="text-sm underline cursor-pointer text-link"
            v-html="t('SETTINGS_FAQS')"
            v-tracker="{
              id: 'settings',
              action: 'click',
              data: {
                target: 'faq',
              },
            }"
            @click="push('faq')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting {
  background: url('/imgs/bg_dark_blur.png') center center no-repeat;
  background-size: 100% 100%;
  background-color: #000;
  backdrop-filter: blur(5px);
  padding-top: 80px;

  &-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .language {
    width: 70%;
    background: #04081d;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
    border: 1px solid #00e0ff;
  }
}
</style>
