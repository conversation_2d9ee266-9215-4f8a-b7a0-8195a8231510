<script setup lang="ts">
import { BUSINESS_EMAIL_REGEX, EMAIL_REGEX, getSocials } from '@utils';
import { useFetchQueries, usePageTracker } from '@composables';
import { useForm } from 'vee-validate';
import { ReminderPayload, TimelineLogs } from '@types';
import { useTimelineReminderMutation } from '@services';
import * as yup from 'yup';

interface Emits {
  (event: 'close'): void;
}

interface Props {
  timeline: TimelineLogs;
}

interface FormOption {
  value: 'brand' | 'player';
  label: string;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const mutation = useTimelineReminderMutation();

const { timelineQuery } = useFetchQueries();
const { t } = useI18n();
const { track } = usePageTracker();

const isSubmitted = ref<boolean>(false);

const OPTIONS: FormOption[] = [
  { value: 'brand', label: t('TIMELINE_INTTOJOINGAMEAS_1') },
  { value: 'player', label: t('TIMELINE_INTTOJOINGAMEAS_2') },
];

const validationSchema = yup.object({
  type: yup.string().required(t('TIMELINE_TYPE_REQUIRED')),
  email: yup
    .string()
    .required(t('TIMELINE_EMAILADDRESS_REQUIRED'))
    .test('email-test', t('TIMELINE_EMAILADDRESS_INVALID'), (value) => {
      if (!value) return false;
      return EMAIL_REGEX.test(value);
    })
    .test('email-test', t('TIMELINE_REGISTER_BRAND_NOTE'), (value) => {
      const { type } = values;
      if (!value) return false;
      if (type === 'brand') return BUSINESS_EMAIL_REGEX.test(value);
      return EMAIL_REGEX.test(value);
    }),
});

const { handleSubmit, values } = useForm<ReminderPayload>({
  initialValues: {
    email: '',
    type: '' as 'brand' | 'player',
    season_id: props.timeline._id,
  },
  validationSchema,
});

const onSubmit = handleSubmit(async (values: ReminderPayload): Promise<void> => {
  try {
    await mutation.mutateAsync(values);
    await timelineQuery.refetch();
    track({
      id: 'register_interest',
      action: 'click',
      data: {
        target: 'submit_interest',
        season_id: props.timeline._id,
        type: values.type,
        email: values.email,
      },
    });
    isSubmitted.value = true;
  } catch (error) {
    console.error('Error submitting interest:', error);
  }
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div
        v-html="!isSubmitted ? timeline.hunt_name : t('TIMELINE_EMAILADDRESSSUBMITTED_TITLE')"
      ></div>
    </template>
    <q-form @submit.prevent="onSubmit" class="text-center season-consent-form">
      <section v-show="!isSubmitted">
        <div
          class="mb-4 text-sm text-center"
          v-html="t('TIMELINE_REGISTERINTERESTFORM_DESC')"
        ></div>
        <VeeInput name="email" :label="t('TIMELINE_EMAILADDRESS_LABEL')" class="mb-5" autofocus />

        <VeeSelect name="type" :label="t('TIMELINE_TYPE_LABEL')" :options="OPTIONS" class="mb-5" />

        <Button
          track-id="disable-track"
          :label="t('TIMELINE_REGISTERINTERESTFORM_BUTTON')"
          :loading="mutation.isPending.value"
          type="submit"
        />
      </section>
      <section v-show="isSubmitted">
        <div
          class="mb-4 text-sm text-center"
          v-html="
            t(
              values.type === 'brand'
                ? 'TIMELINE_EMAILADDRESSSUBMITTED_BUSINESS_DESC'
                : 'TIMELINE_EMAILADDRESSSUBMITTED_PLAYER_DESC',
              {
                HUNT_NAME: timeline.hunt_name,
              },
            )
          "
        ></div>
        <div class="mb-5 text-lg font-bold text-center" v-html="values.email"></div>
        <div class="mt-5 text-center">
          <div class="px-10 mb-5 text-sm" v-html="t('TIMELINE_REGISTER_SOCIALMEDIA')"></div>
          <div class="flex items-center justify-center gap-4">
            <a
              :href="link"
              target="_blank"
              rel="noopener noreferrer"
              v-for="{ link, icon } in getSocials()"
              :key="icon"
            >
              <Icon :name="icon" :size="25" />
            </a>
          </div>
        </div>
      </section>
    </q-form>
  </Dialog>
</template>
