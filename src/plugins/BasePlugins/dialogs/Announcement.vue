<script lang="ts" setup>
import { useUserStore } from '@stores';
import { useReadAnnouncementMutation } from '@services';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const readMutation = useReadAnnouncementMutation();

const { notifications } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();

const notification = computed(() => {
  return notifications.value[0];
});

const loading = computed(() => {
  return readMutation.isPending.value;
});

const skipToday = ref(false);

const heading = computed(() => {
  if (!notification.value?.body) return '';
  return notification.value.body.find((b) => b.type === 'heading')?.data?.key || '';
});

const body = computed(() => {
  if (!notification.value?.body) return [];
  return notification.value.body.filter((b) => b.type !== 'heading') || [];
});

const actions = computed(() => {
  if (!notification.value?.footer?.actions) return [];
  return (
    notification.value.footer.actions.filter((a) => ['button', 'button_link'].includes(a.type)) ||
    []
  );
});

const actionCheckbox = computed(() => {
  if (!notification.value?.footer?.actions) return undefined;
  return notification.value.footer.actions.find((a) => a.type === 'checkbox');
});

async function readAnnouncement(): Promise<void> {
  try {
    if (!notification.value) return;

    await readMutation.mutateAsync({
      id: notification.value._id,
      skip: skipToday.value,
    });
    storeUser.notifications.shift();
    emits('close');
    if (notifications.value.length > 0) {
      skipToday.value = false;
      openDialog('announcement');
    }
  } catch (error) {
    console.error('Error reading announcement:', error);
  }
}

async function handleAction(trigger?: string): Promise<void> {
  switch (trigger) {
    case 'close':
      await readAnnouncement();
      break;
    default:
      break;
  }
}
</script>

<template>
  <Dialog v-if="notification" hide-close>
    <template #btnTopRight>
      <Button
        track-id="disable-track"
        v-if="notification.header?.close_able"
        @click="readAnnouncement"
        shape="square"
        size="small"
        :class="{
          'pointer-events-none': loading,
        }"
      >
        <Icon name="cross" :size="16" />
      </Button>
    </template>

    <template #icon-center>
      <Icon
        v-if="notification.header?.icon_url && notification.header?.icon"
        class="absolute left-1/2 -translate-x-1/2 -top-12"
        :name="notification.header.icon_url"
        :size="60"
        type="url"
      />
    </template>

    <template #header>
      <div v-html="t(heading)" />
    </template>

    <template v-if="!!body.length">
      <template v-for="item in body" :key="item.id">
        <div
          v-if="['text', 'title'].includes(item.type) && item?.data?.key"
          class="text-sm mb-4"
          :class="{
            '!text-base font-bold': item.type === 'title',
          }"
          v-html="t(item?.data?.key)"
        ></div>
        <div
          v-if="item.type === 'text_with_purple_frame' && item?.data?.key"
          class="frame text-lg font-bold text-center mb-5"
          v-html="t(item?.data?.key)"
        ></div>
        <q-img
          v-if="item.type === 'media' && item.data?.media_url"
          class="w-full aspect-video mb-4 rounded-lg"
          :src="item.data?.media_url"
        />
        <div
          v-if="item.type === 'media_custom_size' && item.data?.media_url && item.data.media_size"
          class="flex justify-center items-center"
        >
          <q-img class="mb-4" :src="item.data?.media_url" :width="`${item.data.media_size}px`" />
        </div>
        <div
          v-if="
            item.type === 'media_with_background' &&
            item.data?.media_url &&
            item.data.media_size &&
            item.data.bg_url
          "
          class="bg-media"
          :style="{
            backgroundImage: `url(${item.data.bg_url})`,
          }"
        >
          <q-img class="mt-10" :src="item.data?.media_url" :width="`${item.data.media_size}px`" />
        </div>
      </template>
      <div class="flex flex-nowrap justify-center gap-4 mb-5">
        <div v-for="(a, index) in actions" :key="a.id ?? index">
          <Button
            track-id="announcement"
            :track-data="{
              target: 'read_announcement',
            }"
            v-if="a.type === 'button'"
            :variant="a.variant"
            :label="t(String(a.data?.key))"
            @click="handleAction(a.trigger)"
          />
          <a
            v-if="a.type === 'button_link'"
            :href="a.data?.link || ''"
            target="_blank"
            rel="noopener noreferrer"
            v-tracker="{
              id: 'announcement',
              data: {
                target: 'link',
                link: a.data?.link,
              },
            }"
          >
            <Button track-id="disable-track" :variant="a.variant" :label="t(String(a.data?.key))" />
          </a>
        </div>
      </div>
      <div class="text-center" v-if="actionCheckbox">
        <q-checkbox
          v-tracker="{
            id: 'announcement',
            data: {
              target: 'skip_today',
              value: !skipToday,
            },
          }"
          v-model="skipToday"
          :label="t(String(actionCheckbox.data?.key))"
        />
      </div>
    </template>

    <template #bottom-frame>
      <q-img
        v-if="notification.footer?.bottom_frame && notification.footer?.frame_url"
        class="bottom-frame"
        :src="notification.footer.frame_url"
      />
    </template>
  </Dialog>
</template>
<style lang="scss" scoped>
.frame {
  position: relative;
  padding: 10px 40px;
  margin-left: -35px;
  width: calc(100% + 70px);
  background: linear-gradient(88deg, #bc18d7 40.21%, #a150f1 98.03%);
  pointer-events: none;
  &::before {
    position: absolute;
    content: '';
    background-image: url('/imgs/frame-star-bottom.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 21px;
    height: 22px;
    bottom: -5px;
    left: 15px;
  }
  &::after {
    position: absolute;
    content: '';
    background-image: url('/imgs/frame-star-top.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 26px;
    height: 30px;
    top: -5px;
    right: 15px;
  }
}

.bg-media {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 80px);
  aspect-ratio: 1/1;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: -80px 0 -40px -40px;
}

.bottom-frame {
  position: absolute;
  width: calc(100% + 30px);
  bottom: -8px;
  left: -15px;
  z-index: 10;
  pointer-events: none;
}
</style>
