<script lang="ts" setup>
import gsap, { Elastic, Linear } from 'gsap';
import { playSFX } from '@utils';

type ButtonType =
  | 'default'
  | 'fromDailyMission'
  | 'fromToolKitMission'
  | 'backToResult'
  | 'backToMap';

interface Emits {
  (event: 'close'): void;
}

interface Props {
  crystal: number;
  beacon?: number;
  buttonType?: ButtonType;
  tapAnyWhere?: boolean;
}

interface StarPosition {
  size: number;
  top: string;
  left: string;
  bottom: string;
  right: string;
}

const NO_BACK_BUTTON: ButtonType[] = ['backToMap', 'backToResult'];

const STAR_POSITIONS: StarPosition[] = [
  { size: 50, top: '0px', left: '0px', bottom: 'auto', right: 'auto' },
  { size: 30, top: 'auto', left: 'auto', bottom: '20px', right: '20px' },
] as const;

const emits = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  buttonType: 'default',
  beacon: 0,
});

const { t } = useI18n();
const { push, closeAllDialogs } = useMicroRoute();

const timelines = ref<gsap.core.Timeline[]>([]);

const shouldHideBackButton = computed(
  () => NO_BACK_BUTTON.includes(props.buttonType) || props.tapAnyWhere,
);

const rewardText = computed(() => {
  const { crystal, beacon = 0 } = props;

  if (crystal > 0 && beacon > 0)
    return t('PROMOCODE_CRYSTALSRECEIVED_TEXT_4', { CRYSTAL: crystal, BEACON: beacon });
  else if (crystal > 0 && !beacon)
    return t('PROMOCODE_CRYSTALSRECEIVED_TEXT_2', { CRYSTAL: crystal });
  return t('PROMOCODE_CRYSTALSRECEIVED_TEXT_5', { BEACON: beacon });
});

const rewardIcon = computed(() => {
  const { crystal, beacon = 0 } = props;
  if (crystal > 0 && beacon > 0) return 'beacon-crystals';
  if (crystal > 0 && !beacon) return 'crystal-s';
  return 'beacon';
});

function createFlareAnimation(): gsap.core.Timeline {
  const tl = gsap.timeline();

  tl.fromTo(
    '.upload_flare',
    { scale: 0 },
    {
      scale: 1,
      duration: 1,
      delay: 0.5,
    },
  )
    .fromTo(
      '.btn_s',
      { opacity: 0, y: 15 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
      },
    )
    .fromTo(
      '.upload_flare',
      { rotate: 0 },
      {
        rotate: 720,
        duration: 20,
        repeat: -1,
        ease: Linear.easeNone,
      },
      '-=1',
    );

  return tl;
}

function createMainAnimation(): gsap.core.Timeline {
  const tl = gsap.timeline();

  tl.fromTo(
    '.cry',
    { y: -500 },
    {
      ease: Elastic.easeInOut.config(1, 0.35),
      y: 0,
      duration: 1.5,
    },
  )
    .fromTo(
      '.a-text',
      { opacity: 0, y: 15 },
      {
        opacity: 1,
        y: 0,
        duration: 2,
      },
      '-=1',
    )
    .fromTo(
      '.star',
      { scale: 0 },
      {
        scale: 1,
        duration: 1,
        stagger: {
          each: 0.5,
          repeat: -1,
          yoyo: true,
          ease: 'none',
        },
      },
    );

  return tl;
}

function createStarAnimation(): void {
  gsap.fromTo(
    '.upload_star',
    { opacity: 0 },
    {
      opacity: 1,
      yoyo: true,
      repeat: -1,
      delay: 1,
      duration: 1,
    },
  );
}

function start(): void {
  timelines.value.push(createFlareAnimation());
  timelines.value.push(createMainAnimation());
  createStarAnimation();

  setTimeout(() => playSFX('success'), 1000);
}

function cleanup(): void {
  timelines.value.forEach((tl) => {
    tl.kill();
  });
  gsap.killTweensOf('.upload_star');
  timelines.value.length = 0;
}

function handleGetMore(): void {
  emits('close');
  push('offer_wall');
}

function handleCloseDialog(): void {
  if (props.tapAnyWhere) {
    return emits('close');
  }
}

function handleButtonAction(action: ButtonType): void {
  emits('close');

  switch (action) {
    case 'fromToolKitMission':
      push('missions');
      break;
    case 'backToMap':
      closeAllDialogs();
      break;
  }
}

onMounted(async () => {
  await nextTick();
  start();
});

onBeforeUnmount(() => {
  cleanup();
});
</script>
<template>
  <div
    class="fit relative z-10 flex flex-col justify-center items-center overflow-hidden bg-[#090422] text-center"
    @click="handleCloseDialog"
  >
    <!-- Header -->
    <div class="fixed z-20 flex justify-between top-3 left-3 right-3">
      <Button
        shape="square"
        variant="secondary"
        track-id="disable-track"
        @click="emits('close')"
        :class="{ 'opacity-0 pointer-events-none': shouldHideBackButton }"
      >
        <Icon name="arrow-left" />
      </Button>
      <HeaderCrystal />
    </div>

    <!-- Main Content -->
    <div>
      <p class="a-text" v-html="t('PROMOCODE_CRYSTALSRECEIVED_TEXT_1')"></p>
      <p class="a-text my-[10px] font-bold text-2xl" v-html="rewardText"></p>
      <p class="a-text" v-html="t('PROMOCODE_CRYSTALSRECEIVED_TEXT_3')"></p>

      <!-- Animation Container -->
      <div class="relative flex pointer-events-none full-width flex-center" style="height: 100vw">
        <Icon
          class="absolute upload_flare"
          style="top: 0; left: 50%; width: 100vw; transform: translateX(-50%)"
          name="upload_flare"
        />
        <Icon
          class="absolute upload_star full-width"
          style="top: 0; left: 0; z-index: 2"
          name="star_frame"
        />

        <!-- Reward Icon -->
        <div class="relative">
          <Icon :name="rewardIcon" :size="150" class="relative cry" style="z-index: 3" />

          <!-- Stars -->
          <Icon
            v-for="(star, index) in STAR_POSITIONS"
            :key="`star-${index}`"
            name="star"
            class="star"
            :width="star.size"
            :style="`position:absolute;left:${star.left};bottom:${star.bottom};right:${star.right};top:${star.top};z-index:4;`"
          />
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="btn_s" v-if="!tapAnyWhere">
        <Button
          v-if="buttonType === 'default'"
          class="mx-auto"
          track-id="disable-track"
          @click="handleGetMore"
          :label="t('BUTTON_GETMORECRYSTALS')"
        />

        <Button
          v-else-if="buttonType === 'backToResult'"
          class="mx-auto"
          track-id="disable-track"
          @click="emits('close')"
          :label="t('Back to results')"
        />

        <Button
          v-else-if="buttonType === 'fromDailyMission'"
          class="mx-auto"
          track-id="disable-track"
          :label="t('BUTTON_BACK_TO_MISSIONS')"
          @click="emits('close')"
        />

        <Button
          v-else-if="buttonType === 'fromToolKitMission'"
          class="mx-auto"
          track-id="disable-track"
          :label="t('BUTTON_BACK_TO_MISSIONS')"
          @click="() => handleButtonAction('fromToolKitMission')"
        />

        <Button
          v-else-if="buttonType === 'backToMap'"
          class="mx-auto"
          track-id="disable-track"
          :label="t('BUTTON_BACK_TO_MAP')"
          @click="() => handleButtonAction('backToMap')"
        />
      </div>

      <!-- Tap Anywhere Text -->
      <div
        v-if="tapAnyWhere"
        class="btn_s text-sm text-center"
        v-html="t('PROMOCODE_CRYSTALSRECEIVED_TAP_ANYWHERE')"
      />
    </div>
  </div>
</template>
