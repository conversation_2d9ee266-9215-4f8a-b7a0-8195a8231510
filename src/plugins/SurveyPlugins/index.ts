import { GamePlugin } from '@core/SeasonManager';
import { <PERSON><PERSON>ur<PERSON>, MidSurvey } from './dialogs';

class GameManagerPlugin extends GamePlugin {
  override getPublicAPI<T>(): T {
    throw new Error('Method not implemented.');
  }
}

export const SurveyPlugin = new GameManagerPlugin(
  'SurveyPlugin',
  [],
  [
    {
      path: 'mid_survey',
      component: MidSurvey,
      actived: false,
    },
    {
      path: 'end_survey',
      component: EndSurvey,
      actived: false,
    },
  ],
  [],
);
