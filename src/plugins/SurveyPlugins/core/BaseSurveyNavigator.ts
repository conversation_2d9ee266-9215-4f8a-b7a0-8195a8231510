import { SurveyQuestion } from '@types';

export abstract class BaseSurveyNavigator {
  constructor(protected t: (key: string) => string) {}

  protected getQuestionId(titleKey: string): string {
    return titleKey.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  skipTo = (titleKey: string) => (): string => this.getQuestionId(titleKey);

  abstract getNavigationConditions(): Record<string, (currentQuestion?: SurveyQuestion) => string>;
}
