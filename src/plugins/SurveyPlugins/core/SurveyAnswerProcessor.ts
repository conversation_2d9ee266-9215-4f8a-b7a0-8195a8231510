import { SurveyQuestion } from '@types';

export class SurveyAnswerProcessor {
  static getAnswerForSingleSelection(question: SurveyQuestion): unknown {
    return question.a
      .filter((answer) => answer.selected || (question.type !== 'select' && answer.value))
      .map((answer) => answer.value)[0];
  }

  static getAnswerForMultipleSelection(question: SurveyQuestion): unknown[] {
    return question.a.filter((answer) => answer.selected).map((answer) => answer.value);
  }

  static processQuestionAnswer(question: SurveyQuestion): unknown {
    return question.multiple
      ? this.getAnswerForMultipleSelection(question)
      : this.getAnswerForSingleSelection(question);
  }

  static createSurveyPayload(questions: SurveyQuestion[], seasonId: string) {
    return questions
      .map((question) => ({
        id: question.id,
        question: question.q,
        answer: this.processQuestionAnswer(question),
        season_id: seasonId,
      }))
      .filter(Boolean);
  }
}
