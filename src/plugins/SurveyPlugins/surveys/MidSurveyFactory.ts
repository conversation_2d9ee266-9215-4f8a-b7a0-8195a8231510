import { BaseSurveyFactory, QuestionConfig, BaseSurveyNavigator } from '../core';
import { SurveyQuestion } from '@types';

export class MidSurveyNavigator extends BaseSurveyNavigator {
  getNavigationConditions() {
    return {
      whichCoinHuntCondition: this.whichCoinHuntCondition,
      huntingExperienceCondition: this.huntingExperienceCondition,
      powerupUsefulCondition: this.powerupUsefulCondition,
      skipToReccHTM: this.skipTo('MID_SURVEYQUESTION_RECC_HTM'),
      skipToHelpfulTips: this.skipTo('MID_SURVEYQUESTION_HELPFUL_TIPS'),
      skipToNewCircleShrink: this.skipTo('MID_SURVEYQUESTION_NEWCIRCLESHRINK'),
    };
  }

  // Navigation condition for question: Which coin hunt
  private whichCoinHuntCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('MID_SURVEYQUESTION_NOTHUNT');

    const hunt4Selected = currentQuestion.a.some(
      (a) => a.selected && a.value === this.t('MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_4'),
    );

    const hunt12Selected = currentQuestion.a.some(
      (a) =>
        a.selected &&
        (a.value === this.t('MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_1') ||
          a.value === this.t('MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_2')),
    );

    if (hunt4Selected) return this.getQuestionId('MID_SURVEYQUESTION_NOTHUNT');
    if (hunt12Selected) return this.getQuestionId('MID_SURVEYQUESTION_HUNTING_EXPERIENCE');
    return this.getQuestionId('MID_SURVEYQUESTION_RECC_HTM');
  };

  // Navigation condition for question: Hunting experience
  private huntingExperienceCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('MID_SURVEYQUESTION_ENJOY_HTM');
    const ratingValue = Number(currentQuestion.a[0]?.value || 0);
    return ratingValue > 6
      ? this.getQuestionId('MID_SURVEYQUESTION_ENJOY_HTM')
      : this.getQuestionId('MID_SURVEYQUESTION_NOTENJOY_HTM');
  };

  // Navigation condition for question: Powerup useful
  private powerupUsefulCondition = (currentQuestion?: SurveyQuestion): string => {
    if (!currentQuestion) return this.getQuestionId('MID_SURVEYQUESTION_POWERUP_APPEALING');
    const hasOption4Selected = currentQuestion.a.some(
      (answer) => answer.selected && answer.value === this.t('MID_SURVEY_OPTIONS_POWERUP_USEFUL_4'),
    );
    return hasOption4Selected
      ? this.getQuestionId('MID_SURVEYQUESTION_NOT_USE_POWERUP')
      : this.getQuestionId('MID_SURVEYQUESTION_POWERUP_APPEALING');
  };
}

export class MidSurveyFactory extends BaseSurveyFactory {
  constructor(t: (key: string) => string) {
    super(t, new MidSurveyNavigator(t));
  }

  getQuestionConfigs(): QuestionConfig[] {
    return [
      {
        order: 1,
        id: 'MID_SURVEYQUESTION_AGE',
        title: 'MID_SURVEYQUESTION_AGE',
        subtitle: 'MID_SURVEY_DESC_AGE',
        type: 'select',
        options: [
          'MID_SURVEY_OPTIONS_AGE_1',
          'MID_SURVEY_OPTIONS_AGE_2',
          'MID_SURVEY_OPTIONS_AGE_3',
          'MID_SURVEY_OPTIONS_AGE_4',
          'MID_SURVEY_OPTIONS_AGE_5',
          'MID_SURVEY_OPTIONS_AGE_6',
          'MID_SURVEY_OPTIONS_AGE_7',
          'MID_SURVEY_OPTIONS_AGE_8',
        ],
      },
      {
        order: 2,
        id: 'MID_SURVEYQUESTION_GENDER',
        title: 'MID_SURVEYQUESTION_GENDER',
        subtitle: 'MID_SURVEY_DESC_GENDER',
        type: 'select',
        options: ['MID_SURVEY_OPTIONS_GENDER_1', 'MID_SURVEY_OPTIONS_GENDER_2'],
      },
      {
        order: 3,
        id: 'MID_SURVEYQUESTION_FINDOUTHTM',
        title: 'MID_SURVEYQUESTION_FINDOUTHTM',
        subtitle: 'MID_SURVEYDESC_FINDOUTHTM',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { title: 'MID_SURVEY_OPTIONS_FINDOUTHTM_1' },
          { title: 'MID_SURVEY_OPTIONS_FINDOUTHTM_2' },
          { key: 'MID_SURVEY_OPTIONS_FINDOUTHTM_3' },
          { key: 'MID_SURVEY_OPTIONS_FINDOUTHTM_4' },
          { key: 'MID_SURVEY_OPTIONS_FINDOUTHTM_5' },
          { key: 'MID_SURVEY_OPTIONS_FINDOUTHTM_6' },
          { key: 'MID_SURVEY_OPTIONS_FINDOUTHTM_7' },
          { title: 'MID_SURVEY_OPTIONS_FINDOUTHTM_8' },
        ],
      },
      {
        order: 4,
        id: 'MID_SURVEYQUESTION_WHICH_COIN_HUNT',
        title: 'MID_SURVEYQUESTION_WHICH_COIN_HUNT',
        subtitle: 'MID_SURVEY_DESC_WHICH_COIN_HUNT',
        type: 'select',
        multiple: true,
        options: [
          'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_1',
          'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_2',
          'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_3',
          'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_4',
        ],
        navigationCondition: 'whichCoinHuntCondition',
      },
      {
        order: 5,
        id: 'MID_SURVEYQUESTION_NOTHUNT',
        title: 'MID_SURVEYQUESTION_NOTHUNT',
        subtitle: 'MID_SURVEYDESC_NOTHUNT',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'MID_SURVEY_OPTIONS_NOTHUNT_1' },
          { key: 'MID_SURVEY_OPTIONS_NOTHUNT_2' },
          { key: 'MID_SURVEY_OPTIONS_NOTHUNT_3' },
          { key: 'MID_SURVEY_OPTIONS_NOTHUNT_4' },
          { title: 'MID_SURVEY_OPTIONS_NOTHUNT_5' },
        ],
        navigationCondition: 'skipToReccHTM',
      },
      {
        order: 6,
        id: 'MID_SURVEYQUESTION_HUNTING_EXPERIENCE',
        title: 'MID_SURVEYQUESTION_HUNTING_EXPERIENCE',
        subtitle: 'MID_SURVEYDESC_HUNTING_EXPERIENCE',
        type: 'rate',
        ratingOptions: {
          minKey: 'MID_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_1',
          maxKey: 'MID_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_2',
        },
        navigationCondition: 'huntingExperienceCondition',
      },
      {
        order: 7,
        id: 'MID_SURVEYQUESTION_ENJOY_HTM',
        title: 'MID_SURVEYQUESTION_ENJOY_HTM',
        subtitle: 'MID_SURVEYDESC_ENJOY_HTM',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'MID_SURVEY_OPTIONS_ENJOY_HTM_1' },
          { key: 'MID_SURVEY_OPTIONS_ENJOY_HTM_2' },
          { key: 'MID_SURVEY_OPTIONS_ENJOY_HTM_3' },
          { key: 'MID_SURVEY_OPTIONS_ENJOY_HTM_4' },
          { title: 'MID_SURVEY_OPTIONS_ENJOY_HTM_5' },
        ],
      },
      {
        order: 8,
        id: 'MID_SURVEYQUESTION_NOTENJOY_HTM',
        title: 'MID_SURVEYQUESTION_NOTENJOY_HTM',
        subtitle: 'MID_SURVEYDESC_NOTENJOY_HTM',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_1' },
          { key: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_2' },
          { key: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_3' },
          { title: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_4' },
        ],
      },
      {
        order: 9,
        id: 'MID_SURVEYQUESTION_RECC_HTM',
        title: 'MID_SURVEYQUESTION_RECC_HTM',
        subtitle: 'MID_SURVEYDESC_RECC_HTM',
        type: 'rate',
        ratingOptions: {
          minKey: 'MID_SURVEY_OPTIONS_RECC_HTM_1',
          maxKey: 'MID_SURVEY_OPTIONS_RECC_HTM_2',
        },
      },
      {
        order: 10,
        id: 'MID_SURVEYQUESTION_SENTOSA_CHECKIN',
        title: 'MID_SURVEYQUESTION_SENTOSA_CHECKIN',
        subtitle: 'MID_SURVEYDESC_SENTOSA_CHECKIN',
        type: 'rate',
        ratingOptions: {
          minKey: 'MID_SURVEY_OPTIONS_SENTOSA_CHECKIN_1',
          maxKey: 'MID_SURVEY_OPTIONS_SENTOSA_CHECKIN_2',
        },
      },
      {
        order: 11,
        id: 'MID_SURVEYQUESTION_ATTRACTIVE_DISCOVERY',
        title: 'MID_SURVEYQUESTION_ATTRACTIVE_DISCOVERY',
        subtitle: 'MID_SURVEYDESC_ATTRACTIVE_DISCOVERY',
        type: 'rate',
        ratingOptions: {
          minKey: 'MID_SURVEY_OPTIONS_ATTRACTIVE_DISCOVERY_1',
          maxKey: 'MID_SURVEY_OPTIONS_ATTRACTIVE_DISCOVERY_2',
        },
      },
      {
        order: 12,
        id: 'MID_SURVEYQUESTION_USEFUL_HINTS',
        title: 'MID_SURVEYQUESTION_USEFUL_HINTS',
        subtitle: 'MID_SURVEYDESC_USEFUL_HINTS',
        type: 'rate',
        ratingOptions: {
          minKey: 'MID_SURVEY_OPTIONS_USEFUL_HINTS_1',
          maxKey: 'MID_SURVEY_OPTIONS_USEFUL_HINTS_2',
        },
        navigationCondition: 'skipToHelpfulTips',
      },
      {
        order: 13,
        id: 'MID_SURVEYQUESTION_HINTS_FEEDBACK',
        title: 'MID_SURVEYQUESTION_HINTS_FEEDBACK',
        subtitle: 'MID_SURVEYDESC_HINTS_FEEDBACK',
        type: 'area',
      },
      {
        order: 14,
        id: 'MID_SURVEYQUESTION_POWERUP_USEFUL',
        title: 'MID_SURVEYQUESTION_POWERUP_USEFUL',
        subtitle: 'MID_SURVEY_DESC_POWERUP_USEFUL',
        type: 'select',
        options: [
          'MID_SURVEY_OPTIONS_POWERUP_USEFUL_1',
          'MID_SURVEY_OPTIONS_POWERUP_USEFUL_2',
          'MID_SURVEY_OPTIONS_POWERUP_USEFUL_3',
          'MID_SURVEY_OPTIONS_POWERUP_USEFUL_4',
        ],
        navigationCondition: 'powerupUsefulCondition',
      },
      {
        order: 15,
        id: 'MID_SURVEYQUESTION_POWERUP_APPEALING',
        title: 'MID_SURVEYQUESTION_POWERUP_APPEALING',
        subtitle: 'MID_SURVEY_DESC_POWERUP_APPEALING',
        type: 'select',
        options: [
          'MID_SURVEY_OPTIONS_POWERUP_APPEALING_1',
          'MID_SURVEY_OPTIONS_POWERUP_APPEALING_2',
          'MID_SURVEY_OPTIONS_POWERUP_APPEALING_3',
        ],
        navigationCondition: 'skipToNewCircleShrink',
      },
      {
        order: 16,
        id: 'MID_SURVEYQUESTION_NOT_USE_POWERUP',
        title: 'MID_SURVEYQUESTION_NOT_USE_POWERUP',
        subtitle: 'MID_SURVEY_DESC_NOT_USE_POWERUP',
        type: 'select',
        multiple: true,
        textAreaOptions: [
          { key: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_1' },
          { key: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_2' },
          { key: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_3' },
          { title: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_4' },
        ],
      },
      {
        order: 17,
        id: 'MID_SURVEYQUESTION_NEWCIRCLESHRINK',
        title: 'MID_SURVEYQUESTION_NEWCIRCLESHRINK',
        subtitle: 'MID_SURVEY_DESC_NEWCIRCLESHRINK',
        type: 'select',
        textAreaOptions: [
          { key: 'MID_SURVEY_OPTIONS_NEWCIRCLESHRINK_1' },
          { key: 'MID_SURVEY_OPTIONS_NEWCIRCLESHRINK_2' },
          { key: 'MID_SURVEY_OPTIONS_NEWCIRCLESHRINK_4' },
          { key: 'MID_SURVEY_OPTIONS_NEWCIRCLESHRINK_5' },
          { title: 'MID_SURVEY_OPTIONS_NEWCIRCLESHRINK_6' },
        ],
      },
      {
        order: 18,
        id: 'MID_SURVEYQUESTION_NEWMECHANICS',
        title: 'MID_SURVEYQUESTION_NEWMECHANICS',
        subtitle: 'MID_SURVEY_DESC_NEWMECHANICS',
        type: 'rate',
        ratingOptions: {
          minKey: 'MID_SURVEY_OPTIONS_NEWMECHANICS_1',
          maxKey: 'MID_SURVEY_OPTIONS_NEWMECHANICS_2',
        },
      },
      {
        order: 19,
        id: 'MID_SURVEYQUESTION_OTHER_FEEDBACK',
        title: 'MID_SURVEYQUESTION_OTHER_FEEDBACK',
        subtitle: 'MID_SURVEYDESC_OTHER_FEEDBACK',
        type: 'area',
      },
    ];
  }
}
