<script setup lang="ts">
import { useUserStore } from '@stores';
import { useClaimEndSurveyRewardMutation, useUpdateEndSurveyMutation } from '@services';
import { SurveyQuestion } from '@types';
import { useFetchQueries } from '@composables';
import { SurveyQuestionItem } from '../components';
import { useEndSurvey } from '../composables';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const updateEndSurveyMutation = useUpdateEndSurveyMutation();
const claimEndSurveyRewardMutation = useClaimEndSurveyRewardMutation();
const storeUser = useUserStore();

const { currentSeason } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { userQuery } = useFetchQueries();
const {
  currentQuestionIndex,
  originalQuestions,
  handleNextQuestion,
  handleBackQuestion,
  createSurveyPayload,
} = useEndSurvey();

const loading = computed(() => {
  return updateEndSurveyMutation.isPending.value || claimEndSurveyRewardMutation.isPending.value;
});

async function handleSubmitSurvey(surveyQuestions: SurveyQuestion[]): Promise<void> {
  try {
    if (!currentSeason.value?.id) return;

    const data = createSurveyPayload(surveyQuestions, String(currentSeason.value.id));

    await updateEndSurveyMutation.mutateAsync({ data });

    const { crystal } = await claimEndSurveyRewardMutation.mutateAsync();
    await userQuery.refetch();
    emits('close');
    openDialog('user_rewards', {
      crystal,
    });
  } catch (error) {
    console.error('Error submitting survey:', error);
  }
}
</script>

<template>
  <Dialog @close="emits('close')">
    <template #btnTopLeft v-if="currentQuestionIndex > 0">
      <Button
        track-id="disable-track"
        shape="square"
        variant="secondary"
        :class="{
          'pointer-events-none': loading,
        }"
        @click="handleBackQuestion"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div>{{ t('SURVEY_POPUP_HEADING') }}</div>
    </template>

    <SurveyQuestionItem
      :loading="loading"
      :questions="originalQuestions"
      :initialQuestionIndex="currentQuestionIndex"
      @next="handleNextQuestion"
      @completed="handleSubmitSurvey"
    />
  </Dialog>
</template>
