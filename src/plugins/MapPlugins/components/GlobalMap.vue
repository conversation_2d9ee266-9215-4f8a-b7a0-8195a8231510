<script lang="ts" setup>
import { useGlobalMap } from '../composables';
import { useMapStore } from '@stores';
import { GeolocateControl } from 'maplibre-gl';
import { GeolocateControl as GeolocateMapControl, Mapbox } from 'vue3-mapbox';
import 'vue3-mapbox/dist/style.css';

const storeMap = useMapStore();

const { isMapLoaded } = storeToRefs(storeMap);

const {
  MAP_OPTIONS,
  loadMap,
  registerMap,
  registerGeoInstance,
  setGeoInstance,
  handleErrorGPS,
  handleSuccessGPS,
} = useGlobalMap();
</script>
<template>
  <Mapbox
    debug
    :options="MAP_OPTIONS"
    @click="
      (e) => {
        console.log(e);
      }
    "
    @register="registerMap"
    @load="(ev) => loadMap(ev.target)"
  >
    <template v-if="isMapLoaded">
      <GeolocateMapControl
        :options="{
          positionOptions: {
            enableHighAccuracy: true,
          },
          trackUserLocation: true,
          showAccuracyCircle: true,
        }"
        @geolocate="handleSuccessGPS"
        @error="handleErrorGPS"
        @trackuserlocationend="setGeoInstance"
        @trackuserlocationstart="setGeoInstance"
        @register="(g) => registerGeoInstance(g as unknown as GeolocateControl)"
      />
    </template>
  </Mapbox>
</template>
