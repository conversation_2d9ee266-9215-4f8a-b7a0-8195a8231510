// deck.gl RiveLayer: renders Rive animation textures using shared WebGL context
import { Layer, project32, picking, UNIT } from '@deck.gl/core';
import { Model, Geometry } from '@luma.gl/engine';
import type { SamplerProps } from '@luma.gl/core';
import { iconUniforms } from './icon-layer-uniforms';
import type {
  LayerProps,
  Accessor,
  Position,
  Color,
  Unit,
  UpdateParameters,
  LayerContext,
  DefaultProps,
} from '@deck.gl/core';

// Use the same shaders as icon-layer for now (replace if needed)
import vs from './icon-layer-vertex.glsl';
import fs from './icon-layer-fragment.glsl';

// Import your RiveManager (to be implemented)
import RiveManager from './RiveManager';

type _RiveLayerProps<DataT> = {
  data: any;
  riveManager: RiveManager;
  sizeScale?: number;
  sizeUnits?: Unit;
  sizeMinPixels?: number;
  sizeMaxPixels?: number;
  atlas: {
    path: string;
    icons: Record<string, number[]>;
    width: number;
    height: number;
    artboard: string;
    stateMachine: string;
  };
  billboard?: boolean;
  alphaCutoff?: number;
  getPosition?: Accessor<DataT, Position>;
  getIcon?: Accessor<DataT, string>;
  getColor?: Accessor<DataT, Color>;
  getSize?: Accessor<DataT, number>;
  getAngle?: Accessor<DataT, number>;
  getPixelOffset?: Accessor<DataT, [number, number]>;
  textureParameters?: SamplerProps | null;
  // New zoom-based sizing props
  zoomBasedSizing?: boolean;
  zoomStops?: Array<{ zoom: number; scale: number }>;
};

export type RiveLayerProps<DataT = unknown> = _RiveLayerProps<DataT> & LayerProps;

const DEFAULT_COLOR: [number, number, number, number] = [0, 0, 0, 255];

const defaultProps: DefaultProps<RiveLayerProps> = {
  sizeScale: 1,
  billboard: true,
  sizeUnits: 'pixels',
  sizeMinPixels: 10,
  sizeMaxPixels: Number.MAX_SAFE_INTEGER,
  alphaCutoff: 0,
  getPosition: (x: any) => [x.x, x.y],
  getSize: (x: any) => x.size,
  getColor: DEFAULT_COLOR,
  getAngle: 0,
  getPixelOffset: [0, 0],
  textureParameters: null,
  zoomBasedSizing: false,
  zoomStops: [
    { zoom: 9, scale: 0.5 },
    { zoom: 12, scale: 1.0 },
    { zoom: 15, scale: 1.5 },
    { zoom: 18, scale: 2.0 },
  ],
};

export default class RiveLayer<DataT = any, ExtraPropsT extends {} = {}> extends Layer<
  ExtraPropsT & Required<_RiveLayerProps<DataT>>
> {
  static override defaultProps = defaultProps;
  static override layerName = 'RiveLayer';

  declare state: {
    model?: Model;
    riveManager: RiveManager;
  };

  override getShaders() {
    return super.getShaders({ vs, fs, modules: [project32, picking, iconUniforms] });
  }
  initializeState() {
    const riveManager = new RiveManager({
      width: this.props.atlas.width,
      height: this.props.atlas.height,
      atlast: this.props.atlas.path,
      device: this.context.device,
      iconMapping: this.props.atlas.icons,
      stateMachine: this.props.atlas.stateMachine,
      artboard: this.props.atlas.artboard,
    });
    this.setState({
      riveManager,
      model: this._getModel(),
    });

    const attributeManager = this.getAttributeManager();
    attributeManager!.addInstanced({
      instancePositions: {
        size: 3,
        type: 'float64',
        fp64: this.use64bitPositions(),
        transition: true,
        accessor: 'getPosition',
      },
      instanceSizes: {
        size: 1,
        transition: true,
        accessor: 'getSize',
        transform: (value) => {
          return value;
        },
      },
      instanceOffsets: {
        size: 2,
        accessor: 'getIcon',
        transform: this.getInstanceOffset.bind(this),
      },
      instanceIconFrames: {
        size: 4,
        accessor: 'getIcon',
        transform: this.getInstanceIconFrame.bind(this),
      },
      instanceColorModes: {
        size: 1,
        type: 'uint8',
        accessor: 'getIcon',
        transform: this.getInstanceColorMode.bind(this),
      },
      instanceColors: {
        size: this.props.colorFormat.length, // RGBA
        type: 'unorm8',
        transition: true,
        accessor: 'getColor',
        defaultValue: DEFAULT_COLOR,
      },
      instanceAngles: {
        size: 1,
        transition: true,
        accessor: 'getAngle',
      },
      instancePixelOffset: {
        size: 2,
        transition: true,
        accessor: 'getPixelOffset',
      },
    });
  }

  override updateState(params: UpdateParameters<this>) {
    super.updateState(params);
    const { changeFlags } = params;
    if (changeFlags.extensionsChanged) {
      this.state.model?.destroy();
      this.state.model = this._getModel();
      this.getAttributeManager()!.invalidateAll();
    }
  }
  override finalizeState(context: LayerContext): void {
    super.finalizeState(context);
  }

  override draw(): void {
    const {
      sizeScale,
      sizeMinPixels,
      sizeMaxPixels,
      sizeUnits,
      billboard,
      alphaCutoff,
      zoomBasedSizing,
    } = this.props;
    const riveTexture = this.state.riveManager.getTexture();
    if (riveTexture) {
      // Calculate final size scale
      let finalSizeScale = sizeScale;
      if (zoomBasedSizing) {
        const zoom = this.context.viewport.zoom;
        finalSizeScale = this.calculateZoomBasedSizeScale(zoom, sizeScale);
      }

      const model = this.state.model!;
      model.shaderInputs.setProps({
        icon: {
          iconsTexture: riveTexture,
          iconsTextureDim: [riveTexture.width, riveTexture.height],
          sizeUnits: UNIT[sizeUnits],
          sizeScale: finalSizeScale,
          sizeMinPixels,
          sizeMaxPixels,
          billboard,
          alphaCutoff,
        },
      });
      model.draw(this.context.renderPass);
      this.setNeedsRedraw();
    }
  }

  override get isLoaded(): boolean {
    return super.isLoaded;
  }

  protected getInstanceOffset(): number[] {
    return [0, 0];
  }

  protected getInstanceColorMode(): number {
    return 0;
  }

  protected getInstanceIconFrame(icon: string): number[] {
    return this.state.riveManager.getIconMapping(icon) as number[];
  }

  /**
   * Calculate zoom-based size scale using linear interpolation
   * Similar to Mapbox style interpolation
   */
  protected calculateZoomBasedSizeScale(zoom: number, baseSizeScale: number): number {
    // Use configurable zoom stops or fallback to defaults
    const zoomStops = this.props.zoomStops || [
      { zoom: 9, scale: 0.5 }, // Small at low zoom
      { zoom: 12, scale: 1.0 }, // Normal at medium zoom
      { zoom: 15, scale: 1.5 }, // Larger at high zoom
      { zoom: 18, scale: 2.0 }, // Largest at very high zoom
    ];

    // Clamp zoom to our range
    const minZoom = zoomStops[0]!.zoom;
    const maxZoom = zoomStops[zoomStops.length - 1]!.zoom;
    const clampedZoom = Math.max(minZoom, Math.min(zoom, maxZoom));

    // Find the two stops to interpolate between
    let lowerStop = zoomStops[0]!;
    let upperStop = zoomStops[zoomStops.length - 1]!;

    for (let i = 0; i < zoomStops.length - 1; i++) {
      const currentStop = zoomStops[i]!;
      const nextStop = zoomStops[i + 1]!;
      if (clampedZoom >= currentStop.zoom && clampedZoom <= nextStop.zoom) {
        lowerStop = currentStop;
        upperStop = nextStop;
        break;
      }
    }

    // Linear interpolation
    const t = (clampedZoom - lowerStop.zoom) / (upperStop.zoom - lowerStop.zoom);
    const interpolatedScale = lowerStop.scale + t * (upperStop.scale - lowerStop.scale);

    return baseSizeScale * interpolatedScale;
  }
  protected _getModel(): Model {
    const positions = [-1, -1, 1, -1, -1, 1, 1, 1];
    return new Model(this.context.device, {
      ...this.getShaders(),
      id: this.props.id,
      bufferLayout: this.getAttributeManager()!.getBufferLayouts(),
      geometry: new Geometry({
        topology: 'triangle-strip',
        attributes: {
          positions: {
            size: 2,
            value: new Float32Array(positions),
          },
        },
      }),
      isInstanced: true,
    });
  }
}
