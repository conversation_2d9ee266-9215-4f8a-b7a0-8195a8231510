import { computed, ref } from 'vue';
import {
  SqkiiVouchersTransactionType<PERSON><PERSON>ls,
  SqkiiVouchersTransactionStatus,
  SqkiiVouchersTransactionType,
} from '@enums';
import { numeralFormat } from '@utils';
import type { SVTransactionHistory } from '@types';
import { useSVHistoryQuery } from '@services';

// ===== TYPES =====
export interface FilterOption {
  label: string;
  value: string;
}

export type TabType = 'All' | SqkiiVouchersTransactionStatus | string;

export interface TransactionHistoryParams {
  filter: string;
  type: string;
  status?: string;
  page: number;
}

// ===== CONSTANTS =====
export const PAGE_SIZE = 20;

export const FILTER_OPTIONS: readonly FilterOption[] = [
  { label: 'All Time', value: 'all' },
  { label: 'Today', value: 'today' },
  { label: 'Last 30 Days', value: '30d' },
  { label: 'Last 60 Days', value: '60d' },
  { label: 'Last 90 Days', value: '90d' },
] as const;

// ===== COMPOSABLE =====
export function useTransactionHistory() {
  // State
  const transactions = ref<SVTransactionHistory[]>([]);
  const totalPage = ref(1);
  const page = ref(1);
  const filterBy = ref<string>('all');
  const tab = ref<TabType>('All');

  // Computed
  const tabLabels = computed((): TabType[] => [
    'All',
    ...Object.values(SqkiiVouchersTransactionTypeLabels),
    ...Object.values(SqkiiVouchersTransactionStatus),
  ]);

  const queryParams = computed((): TransactionHistoryParams => {
    const type = getTransactionTypeFromTab(tab.value);
    const status = getTransactionStatusFromTab(tab.value);

    return {
      filter: filterBy.value,
      type,
      status,
      page: page.value,
    };
  });

  const hasMorePages = computed(() => page.value < totalPage.value);
  const hasTransactions = computed(() => transactions.value.length > 0);

  // Utility functions
  function getTransactionTypeFromTab(tabValue: TabType): string {
    const typeKey = Object.keys(SqkiiVouchersTransactionTypeLabels).find(
      (key) => SqkiiVouchersTransactionTypeLabels[key as SqkiiVouchersTransactionType] === tabValue,
    );
    return typeKey || 'all';
  }

  function getTransactionStatusFromTab(tabValue: TabType): string | undefined {
    return Object.values(SqkiiVouchersTransactionStatus).includes(
      tabValue as SqkiiVouchersTransactionStatus,
    )
      ? tabValue
      : undefined;
  }

  function getTransactionId(transaction: SVTransactionHistory): string {
    return transaction.type === SqkiiVouchersTransactionType.PAYMENT
      ? transaction.payment_id
      : transaction.txn_id.toString();
  }

  function formatAmount(transaction: SVTransactionHistory): string {
    return `${transaction.currency}$ ${numeralFormat(transaction.amount, '0,0.00')}`;
  }

  // Actions
  function setFilter(value: string): void {
    filterBy.value = value;
  }

  function setTab(selectedTab: TabType): void {
    tab.value = selectedTab;
    // Reset pagination when changing tabs
    page.value = 1;
  }

  function loadMore(): void {
    if (hasMorePages.value) {
      page.value++;
    }
  }

  function resetPagination(): void {
    page.value = 1;
  }

  // Query
  const query = useSVHistoryQuery(queryParams, {
    select(response) {
      const { total, data } = response;
      totalPage.value = Math.ceil(total / PAGE_SIZE);
      
      // For pagination: append new data, otherwise replace
      transactions.value = page.value > 1 ? [...transactions.value, ...data] : data;
      
      return response;
    },
  });

  return {
    // State
    transactions: readonly(transactions),
    totalPage: readonly(totalPage),
    page: readonly(page),
    filterBy: readonly(filterBy),
    tab: readonly(tab),
    
    // Computed
    tabLabels,
    queryParams,
    hasMorePages,
    hasTransactions,
    
    // Utility functions
    getTransactionId,
    formatAmount,
    
    // Actions
    setFilter,
    setTab,
    loadMore,
    resetPagination,
    
    // Query
    query,
  };
}
