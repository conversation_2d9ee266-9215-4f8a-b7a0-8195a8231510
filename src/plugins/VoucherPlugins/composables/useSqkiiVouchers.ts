import { useBAStore, useUserStore } from '@stores';

export function useSqkiiVouchers() {
  const DEFAULT_FEATURED_MULTIPLIER = 2;
  const DEFAULT_FIRST_TIME_MULTIPLIER = 5;

  const storeBA = useBAStore();
  const storeUser = useUserStore();

  const { gameSettings } = storeToRefs(storeUser);
  const { brand_actions } = storeToRefs(storeBA);

  const isFeaturedOffer = computed(() => {
    const sv1BrandAction = brand_actions.value.find((ba) => ba.unique_id === 'sv_1');
    return Boolean(sv1BrandAction?.featured);
  });

  const multiplierNumber = computed(() => {
    const multiplier = gameSettings.value?.brand_action?.multiplier;
    return {
      featured: multiplier?.featured || DEFAULT_FEATURED_MULTIPLIER,
      firstTime: multiplier?.first_time || DEFAULT_FIRST_TIME_MULTIPLIER,
    };
  });

  const multipliers = computed(() => ({
    featured: multiplierNumber.value.featured,
    firstTime: multiplierNumber.value.firstTime,
    default: 1,
  }));

  return {
    isFeaturedOffer,
    multipliers,
  };
}
