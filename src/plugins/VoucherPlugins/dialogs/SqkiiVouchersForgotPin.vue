<script lang="ts" setup>
import {
  dateTimeFormat,
  beautifyPhoneNumber,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  timeCountDown,
} from '@utils';
import { useSVStore } from '@stores';
import { useForm } from 'vee-validate';
import { APISVResponseError } from '@types';
import { useNow, usePageTracker } from '@composables';
import { useSVAuthForgotPinMutation, useSVAuthSetPinOTPMutation } from '@services';
import dayjs from 'dayjs';
import * as yup from 'yup';

const ERROR_TYPES = {
  OTP_EXPIRED: 'otp_expired',
  RESEND_LIMITED: 'resend_limited',
  VERIFY_SUCCESS: 'verify_success',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

const storeSV = useSVStore();
const now = useNow();
const setPinOTPMutation = useSVAuthSetPinOTPMutation();
const forgotPinMutation = useSVAuthForgotPinMutation();

const { svUser } = storeToRefs(storeSV);
const { t } = useI18n();
const { openDialog, closeDialog } = useMicroRoute();
const { track } = usePageTracker();

const resendAfter = ref('');
const error = ref('');

const loading = computed(() => setPinOTPMutation.isPending.value);

const countdownResend = computed(() => {
  if (!resendAfter.value) return 0;
  return Math.max(0, +new Date(resendAfter.value) - now.value);
});

const isResendAvailable = computed(() => countdownResend.value <= 0);

const validationSchema = yup.object().shape({
  otp_code: yup
    .string()
    .required(t('SIGNUP_FORM_OTP_REQUIRED'))
    .length(6, t('SIGNUP_FORM_OTP_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    otp_code: '',
  },
  validationSchema,
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.OTP_EXPIRED]: () => {
      error.value = t('FORGOT_PIN_OTP_EXPIRED_CREDENTIALS');
    },
    [ERROR_TYPES.RESEND_LIMITED]: () => {
      error.value = t('FORGOT_PIN_RESEND_LIMIT_CREDENTIALS');
    },
    [ERROR_TYPES.VERIFY_SUCCESS]: () => {
      openDialog('sqkii_vouchers_set_pin', {
        otp: values.otp_code,
      });
      track({
        id: 'sqkii_vouchers_forgot_pin',
        action: 'click',
        data: {
          target: 'verify_otp_success',
        },
      });
      closeDialog('sqkii_vouchers_forgot_pin');
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = data?.verify
    ? errorHandlers[ERROR_TYPES.VERIFY_SUCCESS]
    : errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

const onSubmit = handleSubmit(async (): Promise<void> => {
  try {
    await setPinOTPMutation.mutateAsync({
      otp_code: values.otp_code,
    });
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
});

async function sendOTP(): Promise<void> {
  try {
    if (!svUser.value?.mobile_number) throw new Error('Mobile number not available');
    const data = await forgotPinMutation.mutateAsync(svUser.value.mobile_number);
    resendAfter.value = data.resend_after;
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

async function handleResendOTP(): Promise<void> {
  await sendOTP();
}

async function handleClick(e: Event): Promise<void> {
  const target = e.target as HTMLElement;
  if (target.id === 'FORGOT_PIN_RESEND_OTP') {
    await handleResendOTP();
    track({
      id: 'sqkii_vouchers_forgot_pin',
      action: 'click',
      data: {
        target: 'resend_otp',
      },
    });
  }
}

function clearError(): void {
  error.value = '';
}

watch(() => [values.otp_code], clearError);

onMounted(async () => {
  await nextTick();
  await sendOTP();

  addEventListener('click', (e) => {
    void handleClick(e);
  });
});

onBeforeUnmount(() => {
  removeEventListener('click', (e) => {
    void handleClick(e);
  });
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('FORGOT_PIN_HEADER')"></div>
    </template>
    <q-form class="text-center" @submit.prevent="onSubmit">
      <div class="text-sm mb-1" v-html="t('FORGOT_PIN_DESC_1')"></div>
      <div class="text-lg font-bold mb-1" v-if="svUser">
        {{ beautifyPhoneNumber(svUser.mobile_number) }}
      </div>
      <div class="text-sm mb-5" v-html="t('FORGOT_PIN_DESC_2')"></div>
      <VeeOTP class="mb-5" name="otp_code" :num-inputs="6" :error="!!error" />
      <div class="card-error mt-2 mb-5 text-center" v-if="!!error" v-html="t(error)"></div>
      <section v-show="!!resendAfter">
        <div
          class="text-sm mb-5"
          v-html="
            t('FORGOT_PIN_DESC_3', {
              TIME: dateTimeFormat(resendAfter, FULL_DATE_TIME_12H_FORMAT_IN_SECOND),
              TIMEZONE: dayjs().format('Z').replace(':00', ''),
            })
          "
        ></div>
        <div class="text-sm mb-5">
          <span
            v-show="!isResendAvailable"
            v-html="
              t('FORGOT_PIN_DESC_4', {
                TIME: timeCountDown(countdownResend),
              })
            "
          >
          </span>
          <span v-show="isResendAvailable" v-html="t('FORGOT_PIN_DESC_5')"></span>
        </div>
      </section>
      <Button
        track-id="disable-track"
        :label="t('FORGOT_PIN_BTN_NEXT')"
        :loading="loading"
        class="!w-[210px]"
        type="submit"
      />
    </q-form>
  </Dialog>
</template>
