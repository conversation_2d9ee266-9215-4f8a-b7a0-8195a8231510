<script lang="ts" setup>
import { EMAIL_REGEX, successNotify, timeCountDown } from '@utils';
import { useSVStore, useUserStore } from '@stores';
import { useForm } from 'vee-validate';
import { APISVResponseError } from '@types';
import { useFetchQueries, useNow, usePageTracker } from '@composables';
import { useSVAuthLoginMutation, useUpdateSVTokenMutation } from '@services';
import * as yup from 'yup';

interface Props {
  fromRecoverPW?: boolean;
}

const API_ERROR_TYPES = {
  NO_EMAIL: 'no_email',
  LOCKED: 'locked',
  TEMP_LOCKED: 'temp_locked',
  USER_INACTIVE: 'user_inactive',
  FAILED_ATTEMPTS: 'failed_attempts',
  DEFAULT: 'default',
} as const;

type ApiErrorType = (typeof API_ERROR_TYPES)[keyof typeof API_ERROR_TYPES];

defineProps<Props>();

const storeSV = useSVStore();
const storeUser = useUserStore();
const now = useNow();
const mutation = useSVAuthLoginMutation();
const tokenMutation = useUpdateSVTokenMutation();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeAllDialogs, openDialog } = useMicroRoute();
const { svUserQuery, svUserBalanceQuery } = useFetchQueries();
const { track } = usePageTracker();

const error = ref('');
const locked_until = ref('');

const loading = computed(() => mutation.isPending.value || tokenMutation.isPending.value);

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  const lockTime = new Date(locked_until.value).getTime();
  return Math.max(0, lockTime - now.value);
});

const validationSchema = yup.object({
  email: yup.string().required(t('EMAIL_REQUIRED')).matches(EMAIL_REGEX, t('EMAIL_INVALID')),
  password: yup.string().required(t('PASSWORD_REQUIRED')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    email: '',
    password: '',
  },
  validationSchema,
});

const onSubmit = handleSubmit(async (): Promise<void> => {
  try {
    if (!user.value) return;

    const payload = {
      email: values.email,
      password: values.password,
      sdk_linking: {
        hunter_id: user.value.hunter_id,
        user_id: user.value.id,
        mobile_number: user.value.mobile_number,
      },
    };

    const data = await mutation.mutateAsync(payload);
    storeSV.setToken(data.token);
    await Promise.all([
      tokenMutation.mutateAsync(data.token),
      svUserQuery.refetch(),
      svUserBalanceQuery.refetch(),
    ]);
    trackAction('link_account_success');

    closeAllDialogs();
    successNotify({
      message: t('LINK_KEE_SUCCESS'),
    });

    if (!data.user.hasPin) openDialog('sqkii_vouchers_set_pin');
  } catch (error) {
    handleError(error as APISVResponseError);
  }
});

function handleError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [API_ERROR_TYPES.NO_EMAIL]: () => {
      error.value = t('LINK_KEE_INVALID_CREDENTIALS', {
        URL: process.env.APP_SV_URL,
      });
    },
    [API_ERROR_TYPES.LOCKED]: () => {
      error.value = t('LINK_KEE_LOCKED_CREDENTIALS');
    },
    [API_ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('LINK_KEE_MAX_CREDENTIALS');
      locked_until.value = data?.locked_until || '';
    },
    [API_ERROR_TYPES.USER_INACTIVE]: () => {
      openDialog('sqkii_vouchers_create_account', {
        email: values.email,
        stage: 3,
      });
    },
    [API_ERROR_TYPES.FAILED_ATTEMPTS]: () => {
      error.value = t('LINK_KEE_INVALID_CREDENTIALS_ATTEMPTS', {
        URL: process.env.APP_SV_URL,
        ATTEMPTS: 5 - data.failed,
      });
    },
    [API_ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ApiErrorType;

  const handler = data.verify
    ? errorHandlers[API_ERROR_TYPES.FAILED_ATTEMPTS]
    : errorHandlers[errorType] || errorHandlers[API_ERROR_TYPES.DEFAULT];

  handler();
}

function clearError(): void {
  error.value = '';
  locked_until.value = '';
}

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_link_account',
    action: 'click',
    data: { target },
  });
}

function goToForgotPassword(): void {
  trackAction('forgot_password');
  closeAllDialogs();
  openDialog('sqkii_vouchers_forgot_password');
}

watch(() => [values.email, values.password], clearError);
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('LINK_KEE_HEADER')"></div>
    </template>

    <q-form @submit.prevent="onSubmit" class="text-center">
      <!-- Recovery password notification -->
      <div
        v-if="fromRecoverPW"
        class="text-center text-sm mb-5 bg-[#03833E] rounded p-2"
        v-html="t('LINK_KEE_DESC_2')"
      />

      <!-- Description -->
      <div class="text-sm mb-5" v-html="t('LINK_KEE_DESC')" />

      <!-- Form inputs -->
      <VeeInput
        class="mb-5"
        name="email"
        :label="t('LINK_KEE_LABEL_EMAIL')"
        :error="!!error"
        autofocus
      />

      <VeeInput
        class="mb-5"
        name="password"
        :label="t('LINK_KEE_LABEL_PW')"
        :error="!!error"
        type="password"
      />

      <!-- Error message -->
      <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="error" />

      <!-- Submit button -->
      <Button
        track-id="disable-track"
        class="!w-[210px] !mb-5"
        :label="countdown > 0 ? timeCountDown(countdown) : t('LINK_KEE_BTN_SUBMIT')"
        :loading="loading"
        :disabled="countdown > 0"
        type="submit"
      />

      <!-- Forgot password link -->
      <div
        class="text-sm underline text-[#00E0FF] cursor-pointer"
        v-html="t('LINK_KEE_DESC_1')"
        @click="goToForgotPassword"
      />
    </q-form>
  </Dialog>
</template>
