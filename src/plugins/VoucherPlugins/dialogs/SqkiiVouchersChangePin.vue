<script lang="ts" setup>
import { successNotify, timeCountDown } from '@utils';
import { useForm } from 'vee-validate';
import { APISVResponseError } from '@types';
import { useNow, usePageTracker } from '@composables';
import { useSVAuthChangePinMutation } from '@services';
import * as yup from 'yup';

const STAGES = {
  OLD_PIN: 1,
  NEW_PIN: 2,
} as const;

const PIN_LENGTH = 6;

const ERROR_TYPES = {
  TEMP_LOCKED: 'temp_locked',
  INCORRECT_PIN: 'incorrect_pin',
  VERIFY_SUCCESS: 'verify_success',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

type Stage = (typeof STAGES)[keyof typeof STAGES];

interface FormValues {
  old_pin: string;
  pin_code: string;
}

const now = useNow();
const mutation = useSVAuthChangePinMutation();

const { t } = useI18n();
const { closeAllDialogs, openDialog } = useMicroRoute();
const { track } = usePageTracker();

const stage = ref<Stage>(STAGES.OLD_PIN);
const pin_code = ref('');
const error = ref('');
const locked_until = ref('');

const loading = computed(() => mutation.isPending.value);

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return Math.max(0, +new Date(locked_until.value) - now.value);
});

const currentTitle = computed(() =>
  stage.value === STAGES.OLD_PIN ? t('CHANGE_PIN_HEADER') : t('CHANGE_PIN_HEADER_1'),
);

const currentDescription = computed(() =>
  stage.value === STAGES.OLD_PIN ? t('CHANGE_PIN_DESC') : t('CHANGE_PIN_DESC_1'),
);

const submitButtonLabel = computed(() => {
  if (countdown.value > 0) return timeCountDown(countdown.value);
  return stage.value === STAGES.OLD_PIN ? t('CHANGE_PIN_BTN_SUBMIT') : t('CHANGE_PIN_BTN_SUBMIT_1');
});

const canSubmit = computed(() => countdown.value <= 0 && !loading.value);

const validationSchemas = {
  [STAGES.OLD_PIN]: yup.object().shape({
    old_pin: yup
      .string()
      .required(t('PIN_REQUIRED'))
      .length(PIN_LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
  }),
  [STAGES.NEW_PIN]: yup.object().shape({
    pin_code: yup
      .string()
      .required(t('CONFIRM_PIN_REQUIRED'))
      .length(PIN_LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
  }),
};

const validationSchema = computed(() => validationSchemas[stage.value]);

const { handleSubmit, values, setTouched, setFieldValue } = useForm<FormValues>({
  initialValues: {
    old_pin: '',
    pin_code: '',
  },
  validationSchema,
});

function handleError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('LINK_KEE_MAX_CREDENTIALS');
      locked_until.value = data?.locked_until || '';
    },
    [ERROR_TYPES.INCORRECT_PIN]: () => {
      error.value = t('PIN_INVALID_CREDENTIALS_ATTEMPTS');
    },
    [ERROR_TYPES.VERIFY_SUCCESS]: () => {
      pin_code.value = values.old_pin;
      setTouched(false);
      stage.value = STAGES.NEW_PIN;
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = data?.verify
    ? errorHandlers[ERROR_TYPES.VERIFY_SUCCESS]
    : errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_change_pin',
    action: 'click',
    data: {
      target,
    },
  });
}

const onSubmit = handleSubmit(async (values: FormValues): Promise<void> => {
  try {
    console.log('values', values);

    await mutation.mutateAsync({
      old_pin: values.old_pin,
      pin_code: values.pin_code || undefined,
    });
    trackAction('change_pin_success');
    successNotify({
      message: t('CHANGE_PIN_SUCCESS'),
    });
    closeAllDialogs();
  } catch (err) {
    handleError(err as APISVResponseError);
  }
});

function handleGoBack(): void {
  stage.value = STAGES.OLD_PIN;
  setFieldValue('pin_code', '');
}

function handleForgotPin(): void {
  closeAllDialogs();
  openDialog('sqkii_vouchers_forgot_pin');
  trackAction('forgot_pin');
}

function clearError(): void {
  error.value = '';
  locked_until.value = '';
}

watch(() => [values.old_pin, values.pin_code], clearError);
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button
        v-if="stage === STAGES.NEW_PIN"
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="handleGoBack"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div v-html="currentTitle"></div>
    </template>

    <q-form @submit.prevent="onSubmit" class="text-center">
      <div class="text-sm mb-5" v-html="currentDescription"></div>

      <section v-show="stage === STAGES.OLD_PIN">
        <VeeOTP
          class="mb-5"
          name="old_pin"
          :num-inputs="PIN_LENGTH"
          :error="!!error"
          input-type="password"
        />
      </section>

      <section v-show="stage === STAGES.NEW_PIN">
        <VeeOTP
          class="mb-5"
          name="pin_code"
          :num-inputs="PIN_LENGTH"
          :error="!!error"
          input-type="password"
        />
      </section>

      <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="t(error)"></div>

      <div
        v-if="stage === STAGES.OLD_PIN"
        class="underline text-link text-sm mb-5"
        @click="handleForgotPin"
        v-html="t('CHANGE_PIN_DESC_2')"
      ></div>

      <Button
        track-id="disable-track"
        :label="submitButtonLabel"
        :loading="loading"
        :disabled="!canSubmit"
        type="submit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
