<script lang="ts" setup>
import { useMediaDevices, usePageTracker } from '@composables';
import { STORAGE_KEYS } from '@enums';
import { errorNotify } from '@utils';

interface Emits {
  (e: 'scan', code: string): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const { track } = usePageTracker();

const streamVideo = ref<MediaStream | null>(null);

const hasEnabledCamera = computed(() => !!LocalStorage.getItem(STORAGE_KEYS.CAMERA_PERMISSION));

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_scan_promo',
    action: 'click',
    data: {
      target,
    },
  });
}

function requestCameraSuccess(stream: MediaStream): void {
  streamVideo.value = stream;
  closeDialog('camera_permission');
  trackAction('camera_allowed');
}

function requestCameraFailed(): void {
  errorNotify({
    message: t('CAMERA_SCAN_PROMO_TOPUP_NOT_DETECTED'),
    timeout: 8000,
  });
  closeDialog('camera_permission');
  trackAction('camera_blocked');
}

const { request } = useMediaDevices(
  {
    video: {
      facingMode: 'environment',
    },
    audio: false,
  },
  requestCameraSuccess,
  requestCameraFailed,
);

function onDecode(code: string): void {
  emits('scan', code);
  closeDialog('sqkii_vouchers_scan_promo');
}

onMounted(async () => {
  await nextTick();
  if (hasEnabledCamera.value) await request();
  else {
    openDialog('camera_permission', {
      onRequest: request,
      description: t('SQKII_VOUCHERS_CAMERA_PERMISSION_DESC'),
    });
  }
});
</script>

<template>
  <div class="scan flex flex-col flex-nowrap fullscreen overflow-hidden">
    <div class="relative flex justify-center items-center w-full h-16 px-20 mb-5 shrink-0">
      <Button
        track-id="disable-track"
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="closeDialog('sqkii_vouchers_scan_promo')"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="t('SCAN_TOP_UP_PROMO_HEADER')"
      ></div>
    </div>
    <div
      class="flex flex-col flex-nowrap justify-center items-center overflow-y-auto px-10 pb-5 text-center"
    >
      <div class="size-[297px] bg-[#555] rounded-lg mx-auto relative mb-10">
        <StreamBarcodeReader
          :stream="streamVideo"
          @decode="onDecode"
          class="!w-full !h-full rounded-lg"
        />
      </div>
      <div class="px-10 mb-7" v-html="t('SCAN_TOP_UP_PROMO_DESC')"></div>
      <Button
        track-id="disable-track"
        :label="t('SCAN_TOP_UP_PROMO_BTN_ID')"
        variant="purple"
        @click="closeDialog('sqkii_vouchers_scan_promo')"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.scan {
  background: linear-gradient(180deg, #1f7c90 -13.42%, rgba(145, 36, 254, 0) 50%), #090422;
}
</style>
