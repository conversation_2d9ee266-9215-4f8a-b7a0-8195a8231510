<script lang="ts" setup>
import { useFetchQueries, usePageTracker } from '@composables';
import { numeralFormat } from '@utils';
import { useSVTopUpDetailQuery } from '@services';
import { useBAStore, useSVStore } from '@stores';

type TopUpStatus = 'processing' | 'completed' | 'failed';

interface Props {
  status: TopUpStatus;
  confirm_link?: string;
  id?: string;
}

const props = defineProps<Props>();

const storeSV = useSVStore();
const storeBA = useBAStore();

const { svUser } = storeToRefs(storeSV);
const { user_brand_actions } = storeToRefs(storeBA);
const { t } = useI18n();
const { closeAllDialogs, openDialog, push } = useMicroRoute();
const { userQuery } = useFetchQueries();
const { track } = usePageTracker();

const timeRemaining = ref(5);
const redirectInterval = ref<NodeJS.Timeout | null>(null);

const topUpDetailQuery = useSVTopUpDetailQuery(props.id || '', {
  enabled: computed(() => props.status === 'completed' && !!props.id),
  select: (data) => {
    void userQuery.refetch();
    return data;
  },
});

const topUpInfo = computed(() => topUpDetailQuery.data.value);

const headerText = computed(() => {
  const headerMap: Record<TopUpStatus, string> = {
    processing: t('TOP_UP_RESULT_HEADER_1'),
    completed: t('TOP_UP_RESULT_HEADER_2'),
    failed: t('TOP_UP_RESULT_HEADER_3'),
  };
  return headerMap[props.status];
});

const boundUserBA = computed(() => {
  return user_brand_actions.value.find((uba) => uba?.data?.tracking_id === topUpInfo.value?.id);
});

const finalCrystals = computed(() => {
  if (['processing', 'failed'].includes(props.status)) return 0;
  return boundUserBA.value?.metadata.total ?? topUpInfo.value?.rewarded.crystals ?? 0;
});

const formattedBalance = computed(() => {
  if (!svUser.value?.balance || !svUser.value?.currency) return '';
  return `${svUser.value.currency} ${numeralFormat(Number(svUser.value.balance), '0,0.00')}`;
});

function trackAction(action: string): void {
  track({
    id: 'sqkii_vouchers_top_up_result',
    action: 'click',
    data: {
      target: action,
      status: props.status,
    },
  });
}

function handleRedirectToConfirmLink(): void {
  if (!props.confirm_link) return;

  const link = document.createElement('a');
  link.href = props.confirm_link;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';

  link.style.display = 'none';
  document.body.appendChild(link);

  const clickEvent = new MouseEvent('click', {
    view: window,
    bubbles: true,
    cancelable: true,
  });

  link.dispatchEvent(clickEvent);

  setTimeout(() => {
    if (document.body.contains(link)) {
      document.body.removeChild(link);
    }
  }, 100);
}

function startRedirectCountdown(): void {
  if (props.status !== 'processing' || !props.confirm_link) return;

  redirectInterval.value = setInterval(() => {
    timeRemaining.value -= 1;
    if (timeRemaining.value <= 0) {
      cleanupRedirectCountdown();
      handleRedirectToConfirmLink();
    }
  }, 1000);
}

function cleanupRedirectCountdown(): void {
  if (redirectInterval.value) {
    clearInterval(redirectInterval.value);
    redirectInterval.value = null;
  }
}

function handleBackToMain(): void {
  trackAction('back');
  closeAllDialogs();
}

function handleUseVouchers(): void {
  trackAction('use_vouchers');
  closeAllDialogs();
  push('sqkii_vouchers_use_vouchers');
}

function handleTryAgain(): void {
  trackAction('try_again');
  closeAllDialogs();
  openDialog('sqkii_vouchers_get_vouchers');
}

onMounted(async () => {
  await nextTick();
  startRedirectCountdown();
  trackAction('topup_result');
});

onBeforeUnmount(() => {
  cleanupRedirectCountdown();
});
</script>
<template>
  <Dialog hide-close :crystals="!!id">
    <template #header>
      <div v-html="headerText"></div>
    </template>
    <div class="text-center">
      <template v-if="status === 'processing'">
        <Icon class="!w-full rounded-lg mb-5" name="sqkii_voucher_kv" />
        <div
          class="px-5 mb-5 text-sm"
          v-if="timeRemaining"
          v-html="
            t('TOP_UP_RESULT_TEXT_1', {
              TIME: timeRemaining,
            })
          "
        ></div>
        <div
          class="text-sm"
          v-html="
            t('TOP_UP_RESULT_TEXT_2', {
              URL: confirm_link,
            })
          "
        ></div>
      </template>
      <template v-else-if="status === 'completed' && !!topUpInfo">
        <div class="silver-coin">
          <Icon class="mt-10" name="top-up-success" :size="140" />
        </div>
        <div class="-mt-10 text-sm" v-html="t('TOP_UP_RESULT_TEXT_3')"></div>
        <div class="mb-5 text-base font-bold">
          {{ formattedBalance }}
        </div>
        <div class="flex flex-col items-center justify-center mb-5 mx-auto bonus">
          <div class="mt-2 text-sm" v-html="t('TOP_UP_RESULT_TEXT_4')"></div>
          <div class="flex items-center">
            <div class="text-base font-bold">
              {{ numeralFormat(Number(finalCrystals)) }}
            </div>
            <Icon name="crystal" :size="25" />
            <div>!</div>
          </div>
        </div>
        <div class="flex items-center gap-5 flex-nowrap">
          <Button
            track-id="disable-track"
            :label="t('TOP_UP_RESULT_BTN_BACK_MAIN')"
            variant="purple"
            @click="handleBackToMain"
            size="max-content"
            class="flex-1"
          />
          <Button
            track-id="disable-track"
            :label="t('TOP_UP_RESULT_BTN_USE_VOUCHERS')"
            @click="handleUseVouchers"
            size="max-content"
            class="flex-1"
          />
        </div>
      </template>
      <template v-if="status === 'failed'">
        <div class="silver-coin">
          <Icon class="mt-10" name="top-up-failed" :size="140" />
        </div>
        <div class="mb-5 -mt-10 text-sm" v-html="t('TOP_UP_RESULT_TEXT_5')"></div>
        <div class="flex items-center gap-5 flex-nowrap">
          <Button
            track-id="disable-track"
            :label="t('TOP_UP_RESULT_BTN_BACK')"
            variant="purple"
            @click="handleBackToMain"
            size="max-content"
            class="flex-1"
          />
          <Button
            track-id="disable-track"
            :label="t('TOP_UP_RESULT_BTN_TRY_AGAIN')"
            @click="handleTryAgain"
            size="max-content"
            class="flex-1"
          />
        </div>
      </template>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 40px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/big-glow-2.png');
  background-size: cover;
  background-position: center;
  margin-left: -20px;
  margin-top: -50px;
}

.bonus {
  background-image: url(/imgs/top-up-bonus.png);
  max-width: 100%;
  width: 75vw;
  height: 20vw;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
