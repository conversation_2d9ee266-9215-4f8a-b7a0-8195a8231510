<script lang="ts" setup>
import { numeralFormat } from '@utils';
import { useSVStore } from '@stores';
import { useForm } from 'vee-validate';
import { useSVBonusRateQuery } from '@services';
import { usePageTracker } from '@composables';
import { SVUsedPromoCode } from '@types';
import { useSqkiiVouchers } from '../composables';
import * as yup from 'yup';

const LIMITS = {
  MAX_AMOUNT: 1000,
  MIN_AMOUNT: 10,
  MAX_DECIMALS: 2,
} as const;

const INITIAL_VALUES = {
  amount: LIMITS.MIN_AMOUNT,
  promo_code: '',
  bonus_crystals: 0,
};

const storeSV = useSVStore();

const { svUser } = storeToRefs(storeSV);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { isFeaturedOffer, multipliers } = useSqkiiVouchers();
const { track } = usePageTracker();

const validationSchema = yup.object({
  amount: yup
    .string()
    .required(t('AMOUNT_REQUIRED'))
    .test('is-valid-number', t('AMOUNT_INVALID_NUMBER'), isValidNumber)
    .test('valid-decimals', t('AMOUNT_INVALID_DECIMALS'), hasValidDecimals)
    .test('min-amount', t('AMOUNT_INVALID'), (value) => Number(value) >= LIMITS.MIN_AMOUNT),
});

const { handleSubmit, errors, meta, values, setFieldError, setFieldValue } = useForm({
  initialValues: INITIAL_VALUES,
  validationSchema,
});

const bonusRateQuery = useSVBonusRateQuery(
  computed(() => ({
    amount: Number(values.amount),
    promo_code: values.promo_code,
    item: 'crystals',
  })),
);

const baseCrystals = computed(() => {
  return values.amount ? (bonusRateQuery.data.value?.crystals ?? 0) : 0;
});

const currentBalance = computed(() => Number(svUser.value?.balance ?? 0));

const userCurrency = computed(() => svUser.value?.currency ?? 'S$');

const isFirstTimeUser = computed(() => !svUser.value?.total_topup);

const multiplierCalculations = computed(() => {
  const firstTimeMultiplier = isFirstTimeUser.value
    ? multipliers.value.firstTime
    : multipliers.value.default;

  const featuredMultiplier = isFeaturedOffer.value
    ? multipliers.value.featured
    : multipliers.value.default;

  const totalMultiplier = firstTimeMultiplier * featuredMultiplier;

  return {
    firstTime: featuredMultiplier,
    featured: firstTimeMultiplier,
    total: totalMultiplier,
  };
});

const rewardContext = computed(() => {
  const {
    firstTime: firstTimeMultiplier,
    featured: featuredMultiplier,
    total,
  } = multiplierCalculations.value;
  const baseReward = baseCrystals.value;
  const totalReward = baseReward * total + Number(values.bonus_crystals);
  const hasMultiplier = total > 1;

  return {
    isFirstTime: isFirstTimeUser.value,
    isFeatured: isFeaturedOffer.value,
    firstTimeMultiplier,
    featuredMultiplier,
    baseReward,
    totalReward,
    hasMultiplier,
  };
});

function isValidNumber(value: string): boolean {
  if (!value) return false;
  const number = Number(value);
  return !isNaN(number) && isFinite(number);
}

function hasValidDecimals(value: string): boolean {
  if (!value.includes('.')) return true;
  const decimals = value.split('.')[1];
  return decimals ? decimals.length <= LIMITS.MAX_DECIMALS : true;
}

function validateBalance(inputAmount: number): string | null {
  const balance = currentBalance.value;

  if (balance > LIMITS.MAX_AMOUNT) {
    return t('SQKII_VOUCHER_MAX_AMOUNT_1', { MAX_AMOUNT: LIMITS.MAX_AMOUNT });
  }

  if (balance + inputAmount > LIMITS.MAX_AMOUNT) {
    return t('SQKII_VOUCHER_MAX_AMOUNT_2', { MAX_AMOUNT: LIMITS.MAX_AMOUNT });
  }

  return null;
}

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_get_vouchers',
    action: 'click',
    data: {
      target,
    },
  });
}

const onSubmit = handleSubmit((formValues) => {
  const inputAmount = Number(formValues.amount);

  const balanceError = validateBalance(inputAmount);
  if (balanceError) return setFieldError('amount', balanceError);

  const commonPayload = {
    amount: inputAmount,
    promo_code: formValues.promo_code,
  };

  if (isFirstTimeUser.value) {
    trackAction('confirm_crystals');
    return openDialog('sqkii_vouchers_confirm_first_bonus', commonPayload);
  }

  trackAction('enter_pin_top_up');

  return openDialog('sqkii_vouchers_enter_pin_topup', commonPayload);
});

function getRewardDisplayText(): string {
  const { isFeatured, isFirstTime } = rewardContext.value;

  if (isFeatured && isFirstTime) return t('GET_SQKII_VOUCHERS_TEXT_10');

  if (isFirstTime) return t('GET_SQKII_VOUCHERS_TEXT_8');

  return t('GET_SQKII_VOUCHERS_TEXT_4');
}

function getStrikethroughAmount(): number {
  const { isFeatured, baseReward, featuredMultiplier } = rewardContext.value;
  return isFeatured ? baseReward * featuredMultiplier : baseReward;
}

function handleQuestionMark(): void {
  trackAction('question_mark');
  openDialog('sqkii_vouchers_first_bonus_crystals', {
    hiddenButton: true,
    amount: Number(values.amount),
    bonus_crystals: values.bonus_crystals,
  });
}

function handleGetPromoCode(): void {
  trackAction('get_promo_code');
  openDialog('sqkii_vouchers_promo_code', {
    onUsed(data: SVUsedPromoCode) {
      setFieldValue('promo_code', data.promo_code);
      setFieldValue('bonus_crystals', data.bonus_crystals);
    },
  });
}
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('GET_SQKII_VOUCHERS_HEADER')" />
    </template>

    <q-form @submit.prevent="onSubmit">
      <!-- Balance Display -->
      <div class="flex justify-between items-center bg-[#091a3c50] p-3 rounded mb-5">
        <div class="text-xs opacity-50" v-html="t('GET_SQKII_VOUCHERS_TEXT_1')" />
        <div class="text-base">
          {{ userCurrency }}
          {{ numeralFormat(currentBalance, '0,0.00') }}
        </div>
      </div>

      <!-- Amount Input Section -->
      <div class="mb-2 text-sm" v-html="t('GET_SQKII_VOUCHERS_TEXT_2')" />

      <div class="relative">
        <div
          class="absolute z-50 w-10 h-10 text-2xl font-bold text-white pointer-events-none left-3 top-1.5"
          :class="{ '!text-[#ff0000]': errors.amount && meta.touched }"
        >
          {{ userCurrency }}
        </div>
        <VeeInput name="amount" class="relative w-full mb-5" autofocus has-currency />
      </div>

      <div class="mb-5 -mt-3 text-xs opacity-70" v-html="t('GET_SQKII_VOUCHERS_TEXT_3')" />

      <!-- Reward Display Section -->
      <div
        class="relative flex flex-col flex-nowrap items-center justify-center mb-5 bonus p-2 py-6"
        :class="{
          '!max-w-[75vw]': rewardContext.hasMultiplier,
          'mr-6': rewardContext.isFirstTime || rewardContext.isFeatured,
        }"
      >
        <!-- Promo Code Display -->
        <div
          v-if="values.promo_code"
          class="mt-1 text-sm"
          v-html="t('GET_SQKII_VOUCHERS_TEXT_7', { CODE: values.promo_code })"
        />

        <!-- Reward Description -->
        <div
          class="text-sm text-center w-full"
          :class="{ 'order-2': rewardContext.isFirstTime }"
          v-html="getRewardDisplayText()"
        />

        <!-- Reward Amount Display -->
        <div class="flex items-center">
          <div
            v-if="rewardContext.hasMultiplier"
            class="mr-2 text-sm"
            v-html="t('GET_SQKII_VOUCHERS_TEXT_9')"
          />

          <!-- Strikethrough Amount -->
          <div
            v-if="rewardContext.hasMultiplier"
            class="text-base font-bold line-through opacity-50"
          >
            {{ numeralFormat(getStrikethroughAmount()) }}
          </div>

          <!-- Final Amount -->
          <div class="ml-2 text-base font-bold">
            {{ numeralFormat(rewardContext.totalReward) }}
          </div>

          <Icon name="crystal" :size="25" />
          <div v-if="rewardContext.hasMultiplier">!</div>
        </div>

        <!-- Interactive Elements -->
        <Icon
          v-if="rewardContext.isFirstTime"
          class="absolute mt-1 -translate-x-1/2 -translate-y-1/2 right-5 top-1/2 cursor-pointer"
          name="question-mark"
          @click="handleQuestionMark"
        />

        <!-- Promotional Tags -->
        <Icon
          v-if="rewardContext.hasMultiplier"
          class="absolute -right-[3px] top-2/3 translate-x-1/2 pointer-events-none"
          :name="rewardContext.isFeatured ? 'featured_sv_tag' : 'limit-offer'"
          :size="68"
        />

        <!-- <Icon
          v-if="rewardContext.isFeatured"
          class="absolute -right-[0] top-0 -translate-y-[25%] translate-x-[calc(50%-17%)] pointer-events-none"
          name="featured_sv_x3_deco"
          :size="60"
        /> -->
      </div>

      <!-- Action Button -->
      <div class="mb-5 text-center">
        <Button
          track-id="disable-track"
          :label="t('GET_SQKII_VOUCHERS_BTN_NEXT')"
          class="!w-[210px]"
          type="submit"
        />
      </div>

      <!-- Footer Links -->
      <div
        class="underline text-[#00E0FF] text-sm text-center mb-5 cursor-pointer"
        v-html="t('GET_SQKII_VOUCHERS_TEXT_5')"
        @click="handleGetPromoCode"
      />

      <div class="text-xs text-center opacity-70" v-html="t('GET_SQKII_VOUCHERS_TEXT_6')" />
    </q-form>
  </Dialog>
</template>
<style lang="scss" scoped>
.bonus {
  background-image: url(/imgs/top-up-bonus.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
