<script lang="ts" setup>
import { numeralFormat } from '@utils';
import { useSVStore } from '@stores';
import { useSqkiiVouchers } from '../composables';
import { useSVBonusRateQuery } from '@services';
import { usePageTracker } from '@composables';

interface Props {
  amount: number;
  promo_code?: string;
}
const props = defineProps<Props>();

const storeSV = useSVStore();

const { svUser } = storeToRefs(storeSV);
const { openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const { isFeaturedOffer, multipliers } = useSqkiiVouchers();
const { track } = usePageTracker();

const bonusRateQuery = useSVBonusRateQuery(
  computed(() => ({
    amount: props.amount,
    item: 'crystals',
  })),
);

const baseCrystals = computed(() => bonusRateQuery.data.value?.crystals || 0);

const activeMultiplier = computed(() => {
  return isFeaturedOffer.value ? multipliers.value.featured : multipliers.value.firstTime;
});

const crystalRewards = computed(() => {
  const base = baseCrystals.value;
  const originalPrice = isFeaturedOffer.value ? base * multipliers.value.firstTime : base;

  const finalAmount = isFeaturedOffer.value
    ? base * activeMultiplier.value * multipliers.value.firstTime
    : base * activeMultiplier.value;

  return {
    original: numeralFormat(originalPrice),
    final: numeralFormat(finalAmount),
  };
});

const priceDisplay = computed(() => {
  const currency = svUser.value?.currency || 'S$';
  const formattedAmount = numeralFormat(props.amount, '0,0.00');
  return `${currency} ${formattedAmount}`;
});

const descriptionText = computed(() => {
  return t('SV_CONFIRM_FIRST_BONUS_DESC', {
    CURRENTCY: svUser.value?.currency || 'S$',
    AMOUNT: numeralFormat(props.amount, '0,0.00'),
    CRYSTALS: crystalRewards.value.original,
  });
});

function handleMoreCrystals(): void {
  closeDialog('sqkii_vouchers_confirm_first_bonus');
  trackAction('more_crystals');
}

function handleConfirmFirstBonus(): void {
  openDialog('sqkii_vouchers_enter_pin_topup', {
    amount: props.amount,
    promo_code: props.promo_code,
  });
  trackAction('confirm_first_bonus');
}

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_confirm_first_bonus',
    action: 'click',
    data: {
      target,
    },
  });
}
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="closeDialog('sqkii_vouchers_confirm_first_bonus')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('SV_CONFIRM_FIRST_BONUS_HEADER')"></div>
    </template>
    <div class="text-center">
      <div class="text-sm mb-5" v-html="descriptionText"></div>
      <div
        class="bg-[#091A3B] w-max mx-auto px-5 max-w-full rounded py-2 flex flex-nowrap justify-center items-center text-xl font-bold gap-1 mb-8"
      >
        <div class="text-sm" v-html="priceDisplay"></div>
        <div>=</div>
        <div class="line-through opacity-50">
          {{ crystalRewards.original }}
        </div>
        <div>
          {{ crystalRewards.final }}
        </div>
        <Icon name="crystal" :size="20" />
      </div>
      <Button
        track-id="disable-track"
        :label="t('SV_CONFIRM_FIRST_BONUS_BTN_MORE')"
        variant="purple"
        class="!w-[210px] !mb-5"
        @click="handleMoreCrystals"
      />
      <div
        class="text-sm text-link underline"
        v-html="t('SV_CONFIRM_FIRST_BONUS_BTN_YES')"
        @click="handleConfirmFirstBonus"
      ></div>
    </div>
  </Dialog>
</template>
