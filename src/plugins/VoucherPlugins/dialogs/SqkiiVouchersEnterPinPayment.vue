<script lang="ts" setup>
import { useFetchQueries, useGlobalData, useNow } from '@composables';
import { useForm } from 'vee-validate';
import { useUserStore, useSVStore } from '@stores';
import { timeCountDown } from '@utils';
import { useSVPaymentMutation, useSVVerifyPinMutation } from '@services';
import { APISVResponseError, SVPin } from '@types';
import * as yup from 'yup';

const ERROR_TYPES = {
  TEMP_LOCKED: 'temp_locked',
  INCORRECT_PIN: 'incorrect_pin',
  PAYMENT_REJECTED: 'rejected',
  PAYMENT_SERVER_INTERRUPTED: 'server_interrupted',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

interface Props {
  amount: string;
  outlet_id: string;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const now = useNow();
const verifyPinMutation = useSVVerifyPinMutation();
const paymentMutation = useSVPaymentMutation();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeAllDialogs, openDialog, closeDialog, push } = useMicroRoute();
const { campaignName } = useGlobalData();
const { svUserQuery, svUserBalanceQuery } = useFetchQueries();

const error = ref('');
const locked_until = ref('');

const loading = computed(
  () => verifyPinMutation.isPending.value || paymentMutation.isPending.value,
);

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const validationSchema = yup.object().shape({
  pin_code: yup.string().required(t('PIN_REQUIRED')).length(6, t('SIGNUP_FORM_OTP_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    pin_code: '',
    amount: Number(props.amount),
    outlet_id: props.outlet_id,
  },
  validationSchema,
});

async function verifyPinCode(): Promise<SVPin | undefined> {
  try {
    const data = await verifyPinMutation.mutateAsync(values.pin_code);
    return data;
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('PAYMENT_PIN_MAX_CREDENTIALS');
      locked_until.value = data?.locked_until || '';
    },
    [ERROR_TYPES.INCORRECT_PIN]: () => {
      error.value = t('PAYMENT_PIN_INVALID_CREDENTIALS_ATTEMPTS');
    },
    [ERROR_TYPES.PAYMENT_REJECTED]: () => {
      closeAllDialogs();
      openDialog('sqkii_vouchers_payment_result', {
        status: 'processing',
      });
    },
    [ERROR_TYPES.PAYMENT_SERVER_INTERRUPTED]: () => {
      closeAllDialogs();
      openDialog('sqkii_vouchers_payment_result', {
        status: 'processing',
      });
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

const onSubmit = handleSubmit(async (values): Promise<void> => {
  if (!user.value) return;
  try {
    const data = await verifyPinCode();
    if (data?.verify) {
      const payload = {
        ...values,
        pin_code: values.pin_code,
        sdk_linking: {
          user_id: user.value.id,
        },
        campaign: campaignName.value,
      };

      const paymentData = await paymentMutation.mutateAsync(payload);
      await Promise.all([svUserQuery.refetch(), svUserBalanceQuery.refetch()]);
      push(-1);
      closeAllDialogs();
      openDialog('sqkii_vouchers_payment_result', {
        status: 'processing',
        data: paymentData,
      });
    }
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
});

// const onSubmit = handleSubmit(async (values) => {
//   try {

// const callback = handleSubmit(async (values) => {
//   if (!user.value) return;

//   const { data } = await VOUCHERS.payment({
//     ...values,
//     pin_code: values.pin_code,
//     sdk_linking: {
//       user_id: user.value.id,
//     },
//     campaign: process.env.CAMPAIGN_NAME || '',
//   });
//   return data;
// });

// const {
//   loading,
//   status,
//   execute: onSubmit,
// } = useAsync({
//   async fn() {
//     const data = verifyPINCode();
//     return data;
//   },
//   async onSuccess(data) {
//     await storeVouchers.fetchUser();
//     closeAllDialogs();
//     openDialog('payment_result', {
//       data,
//       status: 'processing',
//     });
//   },
//   onError(err) {
//     const { data, message } = err as IAPIVouchersResponseError;
//     switch (true) {
//       case data?.info === 'server_interrupted':
//       case data?.info === 'rejected':
//         closeAllDialogs();
//         openDialog('payment_result', {
//           status: 'processing',
//         });
//         break;
//       default:
//         error.value = t(message);
//         break;
//     }
//   },
//   onSettled() {
//     track('sv_authorization_pin', {
//       type: 'payment',
//       status: status.value,
//     });
//   },
// });

// async function verifyPINCode() {
//   try {
//     const res = await VOUCHERS.verifyPIN(values.pin_code);
//     if (res?.data?.verify) {
//       const data = await callback();
//       return data;
//     }
//   } catch (err) {
//     const { data, message } = err as IAPIVouchersResponseError;
//     switch (true) {
//       case data?.info === 'temp_locked':
//         error.value = t('PAYMENT_PIN_MAX_CREDENTIALS');
//         locked_until.value = data.locked_until;
//         break;
//       case data?.info === 'incorrect_pin' && !!data.failed:
//         error.value = t('PAYMENT_PIN_INVALID_CREDENTIALS_ATTEMPTS', {
//           ATTEMPTS: 5 - data.failed,
//         });
//         break;
//       default:
//         error.value = t(message);
//         break;
//     }
//   }
// }

function handleBack(): void {
  if (loading.value) return;
  closeDialog('sqkii_vouchers_enter_pin_payment');
}

function clearError(): void {
  error.value = '';
}

watch(() => values.pin_code, clearError);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="handleBack"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('ENTER_PIN_TOPUP_HEADER')"></div>
    </template>
    <q-form class="text-center">
      <div class="text-sm mb-5" v-html="t('ENTER_PIN_TOPUP_DESC')"></div>
      <VeeOTP class="mb-5" name="pin_code" :num-inputs="6" :error="!!error" input-type="password" />
      <div class="card-error mt-2 mb-5 text-center" v-if="!!error" v-html="t(error)"></div>

      <div
        class="underline text-link text-sm mb-5"
        @click="
          closeAllDialogs();
          openDialog('forgot_pin');
        "
        v-html="t('ENTER_PIN_TOPUP_DESC_1')"
      ></div>
      <Button
        track-id="disable-track"
        :label="countdown > 0 ? timeCountDown(countdown) : t('ENTER_PIN_TOPUP_BTN_SUBMIT')"
        :disable="countdown > 0"
        :loading="loading"
        @click="onSubmit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
