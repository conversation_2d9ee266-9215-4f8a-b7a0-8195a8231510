<script lang="ts" setup>
import { usePageTracker } from '@composables';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = usePageTracker();

function goToDialog(path: string): void {
  track({
    id: 'sqkii_vouchers_welcome',
    action: 'click',
    data: {
      target: path,
    },
  });
  openDialog(path);
}

function goToSignUp(): void {
  emits('close');
  goToDialog('sign_up');
}

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'GET_STARTED':
      emits('close');
      goToDialog('sqkii_vouchers_link_account_welcome');
      break;
    default:
      break;
  }
}

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('SQKII_VOUCHERS_WELCOME_HEADER')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-sm flex justify-center items-center mb-5"
        v-html="t('SQKII_VOUCHERS_WELCOME_DESC')"
      ></div>
      <SlideIllustration
        class="mb-8"
        :illustrations="[
          { name: 'sqkii_voucher_1', type: 'gif' },
          { name: 'sqkii_voucher_2', type: 'png' },
          { name: 'sqkii_voucher_3', type: 'png' },
          { name: 'sqkii_voucher_4', type: 'png' },
          { name: 'sqkii_voucher_5', type: 'png' },
        ]"
      />
      <template v-if="!user?.mobile_number">
        <div class="text-sm mb-5" v-html="t('SQKII_VOUCHERS_WELCOME_DESC_1')"></div>
        <Button
          track-id="disable-track"
          :label="t('SQKII_VOUCHERS_BTN_SIGN_UP')"
          class="!w-[210px] !mb-5"
          @click="goToSignUp"
        />
        <div
          class="text-sm underline text-[#00E0FF]"
          @click="emits('close')"
          v-html="t('SQKII_VOUCHERS_MAYBE_LATER')"
        ></div>
      </template>

      <template v-else>
        <Button
          track-id="GET_STARTED"
          :label="t('SQKII_VOUCHERS_BTN_GET_STARTED')"
          class="!w-[210px] !mb-5"
          @click="emits('close')"
        />
        <div class="text-sm" v-html="t('SQKII_VOUCHERS_WELCOME_DESC_3')"></div>
      </template>
    </div>
  </Dialog>
</template>
