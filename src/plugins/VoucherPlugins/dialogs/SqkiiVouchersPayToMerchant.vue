<script lang="ts" setup>
import { useSVStore } from '@stores';
import { useForm } from 'vee-validate';
import { numeralFormat } from '@utils';
import { SVOutlet } from '@types';
import * as yup from 'yup';
import { usePageTracker } from '@composables';

interface Props {
  outlet: SVOutlet;
}

interface FormValues {
  outlet_id: string;
  amount: string;
}

const MIN_AMOUNT = 1;
const MAX_DECIMAL_PLACES = 2;
const DEFAULT_CURRENCY = 'S$';

const OUTLET_TYPES = {
  THE_EYES_INC: 'The Eyes Inc',
} as const;

const props = defineProps<Props>();

const storeSV = useSVStore();

const { t } = useI18n();
const { push, closeAllDialogs, openDialog } = useMicroRoute();
const { svUser } = storeToRefs(storeSV);
const { track } = usePageTracker();

const userBalance = computed(() => Number(svUser.value?.balance || 0));
const userCurrency = computed(() => svUser.value?.currency || DEFAULT_CURRENCY);
const formattedBalance = computed(() => numeralFormat(userBalance.value, '0,0.00'));

const outletInfo = computed(() => ({
  name: props.outlet.name,
  address: props.outlet.address,
  type: props.outlet.type,
  minSpend: props.outlet.min_spend,
}));

const isTheEyesInc = computed(() => outletInfo.value.type === OUTLET_TYPES.THE_EYES_INC);
const hasMinSpend = computed(() => outletInfo.value.minSpend && outletInfo.value.minSpend > 0);

const validationSchema = yup.object({
  amount: yup
    .string()
    .required(t('AMOUNT_REQUIRED'))
    .test('is-valid-number', t('AMOUNT_INVALID_NUMBER'), isValidNumber)
    .test('valid-decimals', t('AMOUNT_INVALID_DECIMALS'), hasValidDecimals)
    .test('min-amount', t('AMOUNT_MIN_1'), meetsMinimumAmount)
    .test('within-balance', t('INSUFFICIENT_BALANCE'), isWithinBalance),
});

const { handleSubmit, errors, meta } = useForm<FormValues>({
  initialValues: {
    outlet_id: props.outlet.id,
    amount: '',
  },
  validationSchema,
});

function isValidNumber(value?: string): boolean {
  if (!value) return false;

  const parts = value.split('.');

  if (parts[1] && Number(parts[1]) === 0) return true;

  if (!parts.every((part) => part && !isNaN(Number(part)))) return false;

  return !isNaN(Number(value));
}

function hasValidDecimals(value?: string): boolean {
  if (!value || !value.includes('.')) return true;

  const decimalPart = value.split('.')[1];
  return decimalPart ? decimalPart.length <= MAX_DECIMAL_PLACES : true;
}

function meetsMinimumAmount(value?: string): boolean {
  return value ? Number(value) >= MIN_AMOUNT : false;
}

function isWithinBalance(value?: string): boolean {
  return value ? Number(value) <= userBalance.value : false;
}

const handleFormSubmit = handleSubmit((formData) => {
  openDialog('sqkii_vouchers_enter_pin_payment', {
    amount: formData.amount,
    outlet_id: formData.outlet_id,
  });
  trackAction('enter_pin_payment');
});

function handleGetVouchers(): void {
  push(-1);
  closeAllDialogs();
  trackAction('get_vouchers');
  openDialog('sqkii_vouchers_get_vouchers');
}

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_pay_to_merchant',
    action: 'click',
    data: {
      target,
    },
  });
}

function getTermsAndConditionsText(): string {
  if (isTheEyesInc.value) {
    return t('SV_TAC_DESC_TEI');
  }

  if (hasMinSpend.value) {
    return t('SV_TAC_DESC_1', {
      AMOUNT: outletInfo.value.minSpend,
      NAME: outletInfo.value.name,
    });
  }

  return t('SV_TAC_DESC');
}
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('PAY_TO_MERCHANT_HEADER')" />
    </template>

    <template #btnTopLeft>
      <Button track-id="disable-track" shape="square" variant="secondary" @click="closeAllDialogs">
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>

    <q-form @submit="handleFormSubmit">
      <!-- Outlet Information -->
      <div class="rounded-lg p-4 mb-5" style="background: rgba(93, 58, 192, 0.3)">
        <div class="text-xs opacity-70" v-html="t('PAY_TO_MERCHANT_DESC_2')" />
        <div class="text-base font-bold" v-html="outletInfo.name" />
        <div class="text-sm" v-html="outletInfo.address" />
      </div>

      <!-- Payment Instructions -->
      <div class="text-sm mb-2" v-html="t('PAY_TO_MERCHANT_DESC')" />

      <!-- Amount Input -->
      <div class="relative mb-5">
        <div
          class="absolute z-50 w-10 h-10 text-2xl font-bold text-white pointer-events-none left-3 top-1.5"
          :class="{ '!text-[#ff0000]': errors.amount && meta.touched }"
        >
          {{ userCurrency }}
        </div>
        <VeeInput name="amount" class="relative w-full mb-5" autofocus has-currency />
      </div>

      <!-- Balance Display -->
      <div class="flex flex-col bg-[#091A3C] p-3 rounded mb-5">
        <div class="text-xs opacity-50" v-html="t('PAY_TO_MERCHANT_DESC_1')" />
        <div class="text-base">{{ userCurrency }} {{ formattedBalance }}</div>
      </div>

      <!-- Terms and Conditions -->
      <div class="flex items-start flex-nowrap gap-2 mb-5">
        <Icon name="exclamation-mark" class="size-5 mt-1 flex-shrink-0" />
        <div class="text-xs" v-html="getTermsAndConditionsText()" />
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center flex-nowrap gap-2">
        <Button
          track-id="disable-track"
          :label="t('PAY_TO_MERCHANT_BTN_GET_VOUCHERS')"
          variant="purple"
          size="max-content"
          class="flex-1"
          @click="handleGetVouchers"
        />

        <Button
          track-id="disable-track"
          :label="t('PAY_TO_MERCHANT_BTN_NEXT')"
          type="submit"
          size="max-content"
          class="flex-1"
        />
      </div>
    </q-form>
  </Dialog>
</template>
