<script lang="ts" setup>
import { useSVStore } from '@stores';
import { useForm } from 'vee-validate';
import { SVOutlet } from '@types';
import * as yup from 'yup';
import { numeralFormat } from '@utils';

interface Props {
  outlet: SVOutlet;
}

const props = defineProps<Props>();

const storeSV = useSVStore();

const { svUser } = storeToRefs(storeSV);
const { t } = useI18n();
const { push, closeAllDialogs, openDialog } = useMicroRoute();

const validationSchema = yup.object({
  amount: yup
    .string()
    .required(t('AMOUNT_REQUIRED'))
    .test('invalid', t('AMOUNT_INVALID_NUMBER'), (value) => {
      const data = value.split('.');
      if (data[1] && +data[1] === 0) return true;
      if (!data.map((d) => +d).every(Boolean)) return false;
      return !isNaN(Number(value));
    })
    .test('limit-two-decimals', t('AMOUNT_INVALID_DECIMALS'), (value) => {
      if (!value.includes('.')) return true;
      return value.includes('.') && value.split('.')[1]?.length <= 2;
    })
    .test('min', t('AMOUNT_MIN_1'), (value) => {
      return Number(value) >= 1;
    })
    .test('balance', t('INSUFFICIENT_BALANCE'), (value) => {
      return Number(value) <= Number(user.value?.balance);
    }),
});

const { handleSubmit, errors, meta, values, submitCount } = useForm({
  initialValues: {
    outlet_id: props.outlet.id,
    amount: '',
  },
  validationSchema,
});

const onSubmit = handleSubmit(async () => {
  return openDialog('enter_pin_payment', {
    amount: values.amount,
    outlet_id: values.outlet_id,
  });
});

// watch(submitCount, (count) => {
//   if (count > 0) {
//     if (Number(values.amount) > Number(user.value?.balance)) {
//       track('sv_insufficientbalance_abandon_usevouchers', {
//         amount: Number(values.amount || 0),
//         user_balance: user.value?.balance,
//       });
//     }
//   }
// });
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('PAY_TO_MERCHANT_HEADER')"></div>
    </template>
    <template #btnTopLeft>
      <Button track-id="disable-track" shape="square" variant="secondary" @click="closeAllDialogs">
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>
    <q-form @submit="onSubmit">
      <div class="rounded-lg p-4 mb-5" style="background: rgba(93, 58, 192, 0.3)">
        <div class="text-xs opacity-70" v-html="t('PAY_TO_MERCHANT_DESC_2')"></div>
        <div class="text-base font-bold" v-html="outlet.name"></div>
        <div class="text-sm" v-html="outlet.address"></div>
      </div>
      <div class="text-sm mb-2" v-html="t('PAY_TO_MERCHANT_DESC')"></div>
      <div class="relative mb-5">
        <div
          class="w-10 text-2xl font-bold text-white absolute left-3 top-1/2 -translate-y-1/2 z-50 pointer-events-none"
          :class="{ 'top-[28%]': !!errors.amount && meta.touched }"
        >
          {{ svUser?.currency || 'S$' }}
        </div>
        <VeeInput
          name="amount"
          class="w-full mb-5 relative"
          input-class="!text-2xl font-bold text-right"
          autofocus
        />
      </div>
      <div class="flex flex-col bg-[#091A3C] p-3 rounded mb-5">
        <div class="text-xs opacity-50" v-html="t('PAY_TO_MERCHANT_DESC_1')"></div>
        <div class="text-base">
          {{ svUser?.currency || 'S$' }}
          {{ numeralFormat(Number(svUser?.balance || 0), '0,0.00') }}
        </div>
      </div>
      <div class="flex items-start flex-nowrap gap-2 mb-5">
        <Icon name="exclamation-mark" class="size-5 mt-1" />
        <div
          class="text-xs"
          v-html="
            outlet.type === 'The Eyes Inc'
              ? t('SV_TAC_DESC_TEI')
              : outlet.min_spend && outlet.min_spend > 0
                ? t('SV_TAC_DESC_1', {
                    AMOUNT: outlet.min_spend,
                    NAME: outlet.name,
                  })
                : t('SV_TAC_DESC')
          "
        ></div>
      </div>
      <div class="flex items-center flex-nowrap gap-2">
        <Button
          track-id="disable-track"
          :label="t('PAY_TO_MERCHANT_BTN_GET_VOUCHERS')"
          variant="purple"
          @click="
            push(-1);
            closeAllDialog();
            openDialog('get_sqkii_vouchers');
          "
          size="max-content"
          class="flex-1"
        />
        <Button
          :label="t('PAY_TO_MERCHANT_BTN_NEXT')"
          type="submit"
          size="max-content"
          class="flex-1"
        />
      </div>
    </q-form>
  </Dialog>
</template>
