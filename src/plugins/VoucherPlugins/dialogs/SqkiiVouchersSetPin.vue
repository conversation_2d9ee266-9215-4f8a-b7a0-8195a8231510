<script lang="ts" setup>
import { successNotify } from '@utils';
import { useSVStore } from '@stores';
import { useForm } from 'vee-validate';
import { useSVAuthSetPinMutation, useSVAuthSetPinOTPMutation } from '@services';
import { usePageTracker } from '@composables';
import * as yup from 'yup';

const PIN_LENGTH = 6;

const FORM_STEPS = {
  PIN: 1,
  CONFORM_PIN: 2,
} as const;

interface Props {
  otp?: string;
}

type FormStep = (typeof FORM_STEPS)[keyof typeof FORM_STEPS];

interface FormValues {
  pin_code: string;
  cf_pin_code: string;
}

const props = defineProps<Props>();

const storeSV = useSVStore();
const setPinOTPMutation = useSVAuthSetPinOTPMutation();
const setPinMutation = useSVAuthSetPinMutation();

const { t } = useI18n();
const { closeDialog } = useMicroRoute();
const { track } = usePageTracker();

const step = ref<FormStep>(FORM_STEPS.PIN);

const loading = computed(() => setPinOTPMutation.isPending.value || setPinMutation.isPending.value);

const validationSchema = computed(() => {
  const schema = {
    [FORM_STEPS.PIN]: {
      pin_code: yup
        .string()
        .required(t('PIN_REQUIRED'))
        .length(PIN_LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
    },
    [FORM_STEPS.CONFORM_PIN]: {
      cf_pin_code: yup
        .string()
        .required(t('CONFIRM_PIN_REQUIRED'))
        .length(PIN_LENGTH, t('SIGNUP_FORM_OTP_INVALID'))
        .test('match', t('PIN_NOT_MATCH'), function (value) {
          return this.parent.pin_code === value;
        }),
    },
  };
  return yup.object().shape(schema[step.value]);
});

const { handleSubmit, setTouched, setFieldValue } = useForm<FormValues>({
  initialValues: {
    pin_code: '',
    cf_pin_code: '',
  },
  validationSchema,
});

function handlePinSetSuccess(): void {
  track({
    id: 'sqkii_vouchers_set_pin',
    action: 'click',
    data: {
      target: 'set_pin_success',
    },
  });
  storeSV.updateUser({ hasPin: true });
  successNotify({ message: t('SET_PIN_SUCCESS') });
  closeDialog('sqkii_vouchers_set_pin');
}

async function setPinWithOTP(pinCode: string): Promise<void> {
  try {
    if (!props.otp) {
      throw new Error('OTP is required for this operation');
    }

    await setPinOTPMutation.mutateAsync({
      otp_code: props.otp,
      pin_code: pinCode,
    });
  } catch (error) {
    console.error('Error setting PIN with OTP:', error);
  }
}

async function setPin(pinCode: string): Promise<void> {
  try {
    await setPinMutation.mutateAsync(pinCode);
  } catch (error) {
    console.error('Error setting PIN:', error);
  }
}

const onSubmit = handleSubmit(async (formValues): Promise<void> => {
  try {
    if (step.value === FORM_STEPS.PIN) {
      setTouched(false);
      step.value = FORM_STEPS.CONFORM_PIN;
      return;
    }

    const pinToSet = formValues.cf_pin_code;

    if (props.otp) await setPinWithOTP(pinToSet);
    else await setPin(pinToSet);

    handlePinSetSuccess();
  } catch (error) {
    console.error('PIN set error:', error);
  }
});

function goBackToStage(): void {
  step.value = FORM_STEPS.PIN;
  setFieldValue('cf_pin_code', '');
  setFieldValue('pin_code', '');
  setTouched(false);
  track({
    id: 'sqkii_vouchers_set_pin',
    action: 'click',
    data: {
      target: 'back',
      step: Object.keys(FORM_STEPS)[step.value - 1]?.toLowerCase(),
    },
  });
}
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        v-if="step === FORM_STEPS.CONFORM_PIN"
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="goBackToStage"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div v-html="step === FORM_STEPS.PIN ? t('SET_PIN_HEADER') : t('SET_PIN_HEADER_1')"></div>
    </template>

    <q-form @submit.prevent="onSubmit" class="text-center">
      <div
        class="text-sm mb-5"
        v-html="step === FORM_STEPS.PIN ? t('SET_PIN_DESC') : t('SET_PIN_DESC_1')"
      ></div>

      <section v-show="step === FORM_STEPS.PIN">
        <VeeOTP class="mb-5" name="pin_code" :num-inputs="PIN_LENGTH" input-type="password" />
      </section>

      <section v-show="step === FORM_STEPS.CONFORM_PIN">
        <VeeOTP class="mb-5" name="cf_pin_code" :num-inputs="PIN_LENGTH" input-type="password" />
      </section>

      <Button
        track-id="disable-track"
        :label="step === FORM_STEPS.PIN ? t('SET_PIN_BTN_SUBMIT') : t('SET_PIN_BTN_SUBMIT_1')"
        :loading="loading"
        type="submit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
