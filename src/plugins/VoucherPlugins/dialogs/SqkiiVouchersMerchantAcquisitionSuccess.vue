<script lang="ts" setup>
import { useFetchQueries } from '@composables';
import { useUserStore } from '@stores';

const storeUser = useUserStore();

const { t } = useI18n();
const { closeDialog } = useMicroRoute();
const { svUserSubmittedQuery } = useFetchQueries();

const loading = computed(() => svUserSubmittedQuery.isPending.value);

async function handleClose(): Promise<void> {
  await svUserSubmittedQuery.refetch();
  storeUser.merchantAcquisition.submitted = true;
  closeDialog('sqkii_vouchers_merchant_acquisition_success');
}
</script>

<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('MERCHANT_ACQUISITION_SUBMITTED_HEADER')"></div>
    </template>
    <div class="text-center">
      <div class="silver-coin">
        <Icon class="mt-10" name="top-up-success" :size="140" />
      </div>
      <div class="text-sm -mt-20 mb-5" v-html="t('MERCHANT_ACQUISITION_SUBMITTED_DESC')"></div>
      <Button
        track-id="disable-track"
        :class="{
          'pointer-events-none': loading,
        }"
        :label="t('MERCHANT_ACQUISITION_SUBMITTED_BTN')"
        @click="handleClose"
      />
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 40px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/big-glow-2.png');
  background-size: cover;
  background-position: center;
  margin-left: -20px;
  margin-top: -50px;
}
</style>
