<script lang="ts" setup>
import {
  SqkiiVouchersTransactionStatus,
  SqkiiVouchersTransactionType,
  SqkiiVouchersTransactionTypeLabels,
} from '@enums';
import { numeralFormat } from '@utils';
import { useBAStore } from '@stores';
import { SVTransactionHistory } from '@types';

interface Props {
  transaction: SVTransactionHistory;
}

const props = defineProps<Props>();

const { t } = useI18n();

const storeBA = useBAStore();
const { user_brand_actions } = storeToRefs(storeBA);

const boundUserBA = computed(() => {
  return user_brand_actions.value.find((uba) => uba?.data?.tracking_id === props.transaction.id);
});

const finalCrystals = computed(() => {
  if (!boundUserBA.value?.metadata?.total) return props.transaction?.rewarded?.crystals ?? 0;
  return boundUserBA.value?.metadata?.total;
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('TRANSACTION_DETAILS_HEADER')"></div>
    </template>
    <div class="flex flex-col gap-5 text-center">
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_TYPE')"></div>
        <div>{{ SqkiiVouchersTransactionTypeLabels[transaction.type] }}</div>
      </div>
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_STATUS')"></div>
        <div
          class="font-bold uppercase"
          :class="{
            'text-[#53E56B]': transaction.status == SqkiiVouchersTransactionStatus.COMPLETED,
            'text-[#FF4D4D]': transaction.status === SqkiiVouchersTransactionStatus.FAILED,
            'text-[#BC18D7]': transaction.status === SqkiiVouchersTransactionStatus.REFUNDED,
          }"
        >
          {{ transaction.status }}
        </div>
      </div>
      <div class="flex flex-col gap-1" v-if="transaction.outlet_name">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_MERCHANT')"></div>
        <div>{{ transaction.outlet_name }}</div>
      </div>
      <div class="flex flex-col gap-1" v-if="transaction.amount">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_AMOUNT')"></div>
        <div>
          {{ `${transaction.currency}$ ${numeralFormat(transaction.amount, '0,0.00')}` }}
        </div>
      </div>
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_CRYSTALS')"></div>
        <div class="flex items-center justify-center gap-1">
          <div class="text-sm">
            {{ numeralFormat(finalCrystals ?? 0) }}
          </div>
          <Icon name="crystal-s" />
        </div>
      </div>
      <div class="flex flex-col gap-1">
        <div class="text-sm font-bold" v-html="t('TRANSACTION_DETAILS_ID')"></div>
        <div>
          {{
            transaction.type === SqkiiVouchersTransactionType.PAYMENT
              ? transaction.payment_id
              : transaction.txn_id
          }}
        </div>
      </div>
      <div class="px-5" v-html="t('TRANSACTION_DETAILS_CONTACT')"></div>
    </div>
  </Dialog>
</template>
