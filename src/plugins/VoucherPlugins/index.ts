import { GamePlugin } from '@core/SeasonManager';
import {
  SqkiiVouchers,
  SqkiiVouchersMerchantList,
  SqkiiVouchersOutletDetails,
  SqkiiVouchersSetting,
  SqkiiVouchersTransactionHistory,
} from './views';
import {
  SqkiiVouchersChangeEmail,
  SqkiiVouchersChangePin,
  SqkiiVouchersConfirmFirstBonus,
  SqkiiVouchersCreateAccount,
  SqkiiVouchersEnterPinTopUp,
  SqkiiVouchersFirstBonusCrystals,
  SqkiiVouchersForgotPassword,
  SqkiiVouchersForgotPin,
  SqkiiVouchersGetVouchers,
  SqkiiVouchersLinkAccount,
  SqkiiVouchersLinkAccountWelcome,
  SqkiiVouchersMerchantAcquisition,
  SqkiiVouchersMerchantAcquisitionSuccess,
  SqkiiVouchersPromoCode,
  SqkiiVouchersRecoverPassword,
  SqkiiVouchersScanPromo,
  SqkiiVouchersSetPin,
  SqkiiVouchersTopUpResult,
  SqkiiVouchersWelcome,
} from './dialogs';

class GameManagerPlugin extends GamePlugin {
  override getPublicAPI<T>(): T {
    throw new Error('Method not implemented.');
  }
}

export const VoucherPlugin = new GameManagerPlugin(
  'VoucherPlugin',
  [
    {
      path: 'sqkii_vouchers',
      component: SqkiiVouchers,
      bgm: 'default',
    },
    {
      path: 'sqkii_vouchers_setting',
      component: SqkiiVouchersSetting,
      bgm: 'default',
    },
    {
      path: 'sqkii_vouchers_merchant_list',
      component: SqkiiVouchersMerchantList,
      bgm: 'default',
    },
    {
      path: 'sqkii_vouchers_outlet_details',
      component: SqkiiVouchersOutletDetails,
      bgm: 'default',
    },
    {
      path: 'sqkii_vouchers_transaction_history',
      component: SqkiiVouchersTransactionHistory,
      bgm: 'default',
    },
  ],
  [
    {
      path: 'sqkii_vouchers_welcome',
      component: SqkiiVouchersWelcome,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_create_account',
      component: SqkiiVouchersCreateAccount,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_set_pin',
      component: SqkiiVouchersSetPin,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_link_account_welcome',
      component: SqkiiVouchersLinkAccountWelcome,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_link_account',
      component: SqkiiVouchersLinkAccount,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_change_pin',
      component: SqkiiVouchersChangePin,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_change_email',
      component: SqkiiVouchersChangeEmail,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_forgot_pin',
      component: SqkiiVouchersForgotPin,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_forgot_password',
      component: SqkiiVouchersForgotPassword,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_recover_password',
      component: SqkiiVouchersRecoverPassword,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_first_bonus_crystals',
      component: SqkiiVouchersFirstBonusCrystals,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_merchant_acquisition',
      component: SqkiiVouchersMerchantAcquisition,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_merchant_acquisition_success',
      component: SqkiiVouchersMerchantAcquisitionSuccess,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_get_vouchers',
      component: SqkiiVouchersGetVouchers,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_promo_code',
      component: SqkiiVouchersPromoCode,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_scan_promo',
      component: SqkiiVouchersScanPromo,
      actived: false,
      transitionShow: 'slide-left',
      transitionHide: 'slide-right',
    },
    {
      path: 'sqkii_vouchers_enter_pin_topup',
      component: SqkiiVouchersEnterPinTopUp,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_confirm_first_bonus',
      component: SqkiiVouchersConfirmFirstBonus,
      actived: false,
    },
    {
      path: 'sqkii_vouchers_top_up_result',
      component: SqkiiVouchersTopUpResult,
      actived: false,
    },
  ],
  [],
);
