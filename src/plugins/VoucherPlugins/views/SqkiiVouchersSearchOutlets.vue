<script lang="ts" setup>
import { useSVPaymentScanMutation, useSVRecentOutletQuery } from '@services';
import { errorNotify } from '@utils';
import { SVRecentOutlet, APISVResponseError } from '@types';

const paymentScanMutation = useSVPaymentScanMutation();
const recentOutletsQuery = useSVRecentOutletQuery();

const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const searchQuery = ref<string>('');
const selectedOutlet = ref<SVRecentOutlet | null>(null);

const isLoading = computed(() => paymentScanMutation.isPending.value);
const isQueryLoading = computed(() => recentOutletsQuery.isLoading.value);

const availableOutlets = computed((): SVRecentOutlet[] => {
  return recentOutletsQuery.data.value?.outlets ?? [];
});

const filteredOutlets = computed((): SVRecentOutlet[] => {
  if (!searchQuery.value.trim()) {
    return availableOutlets.value;
  }

  const query = searchQuery.value.toLowerCase().trim();
  return availableOutlets.value.filter(
    (outlet) =>
      outlet.name.toLowerCase().includes(query) || outlet.code.toLowerCase().includes(query),
  );
});

const hasResults = computed(() => filteredOutlets.value.length > 0);
const canProceed = computed(() => Boolean(selectedOutlet.value) && !isLoading.value);

function handleSearchInput(): void {
  if (selectedOutlet.value) {
    selectedOutlet.value = null;
  }
}

function handleOutletSelect(outlet: SVRecentOutlet): void {
  selectedOutlet.value = outlet;
  searchQuery.value = outlet.name;
}

async function handleProceedToPayment(): Promise<void> {
  if (!selectedOutlet.value) return;

  try {
    const outletData = await paymentScanMutation.mutateAsync(selectedOutlet.value.code);
    push(-1);
    openDialog('sqkii_vouchers_pay_to_merchant', {
      outlet: outletData,
    });
  } catch (error) {
    const { message } = error as APISVResponseError;
    errorNotify({
      message: t(message),
    });
  }
}
</script>
<template>
  <div class="search-outlets flex flex-col flex-nowrap fullscreen">
    <!-- Header -->
    <div class="flex justify-center items-center h-16 px-20 relative shrink-0 mb-5">
      <Button
        track-id="disable-track"
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>

      <div class="text-lg font-extrabold text-center w-full" v-html="t('SEARCH_OUTLET_HEADER')" />
    </div>

    <!-- Main Content -->
    <div class="flex flex-col justify-between items-center px-10 pb-5 h-full">
      <!-- Search Section -->
      <div class="w-full">
        <!-- Search Input -->
        <Input
          v-model="searchQuery"
          class="full-width"
          type="search"
          :label="t('FAQ_SEARCH')"
          @update:model-value="handleSearchInput"
        />

        <!-- Loading State -->
        <div
          v-if="isQueryLoading"
          class="rounded-b-lg flex justify-center items-center py-8"
          style="background: rgba(55, 80, 121, 0.3)"
        >
          <q-spinner-dots color="primary" size="20px" />
        </div>

        <!-- Search Results -->
        <div
          v-if="!selectedOutlet"
          class="rounded-b-lg flex flex-col px-5 flex-nowrap gap-3 overflow-y-auto"
          style="background: rgba(55, 80, 121, 0.3)"
          :style="{ maxHeight: '300px' }"
        >
          <!-- Results List -->
          <template v-if="hasResults">
            <div
              v-for="outlet in filteredOutlets"
              :key="outlet.code"
              class="flex flex-col py-3 cursor-pointer hover:bg-white/5 rounded transition-colors [&:not(:last-child)]:border-b [&:not(:last-child)]:border-[#ffffff10]"
              @click="handleOutletSelect(outlet)"
            >
              <div class="text-base font-bold" v-html="outlet.name" />
              <div
                class="text-sm opacity-75"
                v-html="
                  t('SEARCH_OUTLET_QR_ID', {
                    CODE: outlet.code.toUpperCase(),
                  })
                "
              />
            </div>
          </template>

          <!-- No Results -->
          <template v-else>
            <div class="flex flex-col items-center py-8 text-center">
              <div class="text-base font-bold" v-html="t('SEARCH_OUTLET_NO_RESULT')" />
            </div>
          </template>
        </div>
      </div>

      <!-- Action Button -->
      <Button
        track-id="disable-track"
        :label="t('SEARCH_OUTLET_BTN_NEXT')"
        class="!w-[210px]"
        :disable="!canProceed"
        :loading="isLoading"
        @click="handleProceedToPayment"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.search-outlets {
  background: linear-gradient(180deg, #1f7c90 -13.42%, rgba(145, 36, 254, 0) 50%), #090422;
}
</style>
