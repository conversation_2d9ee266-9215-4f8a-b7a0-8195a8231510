<script lang="ts" setup>
import { useFetchQueries, usePageTracker } from '@composables';
import { removeSpecialKey, numeralFormat } from '@utils';
import { useUserStore, useSVStore, useGlobalStore } from '@stores';
import { useScroll, useElementSize, debouncedRef } from '@vueuse/core';
import { SVOutlet } from '@types';
import { useSVOutletsQuery } from '@services';
import { calculateDistance } from '../utils';
import { BrandSponsors } from '@enums';

const SCROLL_THRESHOLD = 80;
const TIMEOUT_DURATION = 60 * 1000; // 1 minute

const storeSV = useSVStore();
const storeGlobal = useGlobalStore();
const storeUser = useUserStore();

const { user, merchantAcquisition } = storeToRefs(storeUser);
const { lastUserLocations } = storeToRefs(storeGlobal);
const { svUser, isSVAuthenticated } = storeToRefs(storeSV);
const { t } = useI18n();
const { push, openDialog, activePage, activeDialog } = useMicroRoute();
const { svUserQuery, svUserBalanceQuery, userQuery } = useFetchQueries();
const { track } = usePageTracker();

const outletsQuery = useSVOutletsQuery();

const scrollerEl = ref<HTMLElement>();
const bannerEl = ref<HTMLElement>();
const tab = ref('All');
const searchInput = ref('');
const search = debouncedRef(searchInput, 300);
const closed = ref(false);
const dialogTimer = ref<NodeJS.Timeout | null>(null);
const bonusDialogShown = ref(false);
const pageEntryTime = ref<number>(0);

const { y } = useScroll(scrollerEl);
const { height } = useElementSize(bannerEl);

const isSVHome = computed(() => activePage.value === 'sqkii_vouchers');
const shoudTriggerBonusDialog = computed(() => {
  const elapsedTime = Date.now() - pageEntryTime.value;
  return (
    isSVHome.value &&
    !bonusDialogShown.value &&
    !activeDialog.value &&
    elapsedTime >= TIMEOUT_DURATION &&
    svUser.value &&
    !svUser.value.total_topup
  );
});

const outlets = computed(() => outletsQuery.data.value ?? {});
const isLoadingOutlets = computed(() => outletsQuery.isLoading.value);

const categories = computed(() => {
  const brandTypes = new Set<string>();

  Object.values(outlets.value).forEach((outletList) => {
    outletList.forEach((outlet) => {
      brandTypes.add(outlet.brand_type);
    });
  });

  return ['All', ...Array.from(brandTypes).sort()];
});

const filteredOutlets = computed(() => {
  const searchTerm = search.value?.toLowerCase().trim() || '';
  const selectedTab = tab.value;

  return Object.entries(outlets.value).filter(([merchantName, outletList]) => {
    // Filter by brand type
    const matchesBrandType =
      selectedTab === 'All' || outletList.every((outlet) => outlet.brand_type === selectedTab);

    if (!matchesBrandType) return false;

    // Filter by search term
    if (!searchTerm) return true;

    const matchesSearch =
      merchantName.toLowerCase().includes(searchTerm) ||
      outletList.some(
        (outlet) =>
          outlet.name.toLowerCase().includes(searchTerm) ||
          outlet.address.toLowerCase().includes(searchTerm),
      );

    return matchesSearch;
  });
});

const sortedOutlets = computed(() => {
  const result: Record<string, SVOutlet[]> = {};

  const userLocation = lastUserLocations.value;

  filteredOutlets.value
    .sort(([a], [b]) => a.localeCompare(b))
    .forEach(([merchantName, outletList]) => {
      result[merchantName] = [...outletList].sort((a, b) => {
        const distanceA = calculateDistance(a.location.coordinates, userLocation);
        const distanceB = calculateDistance(b.location.coordinates, userLocation);
        return distanceA - distanceB;
      });
    });

  return result;
});

const balanceFormatted = computed(() => {
  const currency = svUser.value?.currency ?? 'S$';
  const balance = Number(svUser.value?.balance ?? 0);
  return `${currency} ${numeralFormat(balance, '0,0.00')}`;
});

const shouldShowCallout = computed(
  () =>
    merchantAcquisition.value.show_callout && !closed.value && !merchantAcquisition.value.submitted,
);

function handleGetVouchers(): void {
  if (!svUser.value) handleAuthenticatedAction(() => goToDialog('sqkii_vouchers_create_account'));
  else goToDialog('sqkii_vouchers_get_vouchers');
}

function handleUseVouchers(): void {
  if (!svUser.value) {
    handleAuthenticatedAction(() => goToDialog('sqkii_vouchers_create_account'));
  } else {
    track({
      id: 'sqkii_vouchers',
      action: 'click',
      data: {
        target: 'sqkii_vouchers_use_vouchers',
      },
    });
    push('sqkii_vouchers_use_vouchers');
  }
}

function handleSelectMerchant(merchantName: string, outlets: SVOutlet[]): void {
  push('sqkii_vouchers_merchant_list', {
    merchant: merchantName,
    outlets,
  });
}

function handleAuthenticatedAction(action: () => void): void {
  if (!user.value?.mobile_number) {
    goToDialog('sqkii_vouchers_welcome');
    return;
  }

  action();
}

function goToDialog(path: string): void {
  track({
    id: 'sqkii_vouchers',
    action: 'click',
    data: {
      target: path,
    },
  });
  openDialog(path);
}

function scrollTop(): void {
  if (scrollerEl.value)
    scrollerEl.value.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
}

function handleCloseCallout(): void {
  closed.value = true;
  storeUser.updateMerchantAcquisition('closed_callout');
}

function startBonusDialogTimer(): void {
  clearBonusDialogTimer();

  dialogTimer.value = setTimeout(() => {
    triggerBonusDialog();
  }, TIMEOUT_DURATION);
}

function clearBonusDialogTimer(): void {
  if (dialogTimer.value) {
    clearTimeout(dialogTimer.value);
    dialogTimer.value = null;
  }
}

function triggerBonusDialog(): void {
  if (shoudTriggerBonusDialog.value) {
    bonusDialogShown.value = true;
    goToDialog('sqkii_vouchers_first_bonus_crystals');
  }
}

function resetBonusDialogState(): void {
  bonusDialogShown.value = false;
  pageEntryTime.value = Date.now();
}

watch(
  [isSVHome, activeDialog, svUser],
  ([isHome, dialog, user]) => {
    clearBonusDialogTimer();

    if (isHome && !dialog && user && !user.total_topup) {
      resetBonusDialogState();
      startBonusDialogTimer();
    }
  },
  { immediate: true },
);

onBeforeMount(() => {
  if (!isSVAuthenticated.value) storeSV.$reset();
  storeUser.merchantAcquisition.submitted = false;
});

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'MERCHANT_ACQUISITION':
      goToDialog('sqkii_vouchers_merchant_acquisition');
      break;
    case 'SQKII_VOUCHERS_CREATE':
      handleAuthenticatedAction(() => goToDialog('sqkii_vouchers_create_account'));
      break;
    case 'SQKII_VOUCHERS_LINK':
      handleAuthenticatedAction(() => goToDialog('sqkii_vouchers_link_account_welcome'));
      break;
    default:
      break;
  }
}

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
  storeUser.updateMerchantAcquisition('visited');
  await userQuery.refetch();

  if (isSVAuthenticated.value)
    await Promise.all([svUserQuery.refetch(), svUserBalanceQuery.refetch()]);

  if (!isSVAuthenticated.value) openDialog('sqkii_vouchers_welcome');
  else {
    if (svUser.value && !svUser.value.hasPin) openDialog('sqkii_vouchers_set_pin');
  }
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
  clearBonusDialogTimer();
});
</script>
<template>
  <div
    ref="scrollerEl"
    class="fullscreen bg-[#200D37] overflow-y-auto flex flex-col flex-nowrap scroll-smooth"
  >
    <!-- Header Section -->
    <div ref="bannerEl" class="banner relative flex-shrink-0">
      <div
        class="fixed z-30 flex justify-center items-center w-full h-16 px-20 bg-[#16181D] transition-colors duration-300 ease-out"
        :class="{ 'bg-[#200D37]': y >= height - SCROLL_THRESHOLD }"
        style="will-change: background-color"
      >
        <Button
          track-id="disable-track"
          variant="secondary"
          shape="square"
          class="absolute left-4"
          @click="push(-1)"
        >
          <Icon name="arrow-left" />
        </Button>
        <div
          class="text-lg font-extrabold text-center w-full"
          v-html="t('SQKII_VOUCHERS_HEADER')"
        />
        <HeaderCrystal class="absolute right-4" />
      </div>

      <!-- Balance Card -->
      <div class="card-balance">
        <div class="frame-balance flex flex-nowrap flex-col justify-center">
          <div class="flex items-start justify-between" :class="{ 'mb-5': !!svUser }">
            <div class="flex flex-col mb-2">
              <div
                class="text-base font-medium text-white opacity-70"
                v-html="t('SQKII_VOUCHERS_BALANCE_TITLE')"
              />
              <div class="text-3xl font-bold" v-html="balanceFormatted" />
            </div>
            <Icon
              v-if="!svUser?.id"
              class="absolute right-5 -top-20"
              :name="`/sov/vouchers/${BrandSponsors.Sqkii}`"
              :size="180"
            />
            <div v-if="svUser?.id" class="flex items-center gap-2">
              <Button
                track-id="disable-track"
                :disabled="!svUser"
                shape="square"
                @click="push('sqkii_vouchers_transaction_history')"
              >
                <Icon name="history" />
              </Button>
              <Button
                track-id="disable-track"
                shape="square"
                @click="push('sqkii_vouchers_setting')"
              >
                <Icon name="settings" :size="14" />
              </Button>
            </div>
          </div>

          <div v-if="!svUser" class="flex flex-nowrap items-start gap-2 mb-1">
            <Icon name="exclamation-mark" :size="15" class="mt-1" />
            <div class="text-sm" v-html="t('SQKII_VOUCHERS_BALANCE_DESC')" />
          </div>

          <div class="flex flex-nowrap items-center gap-4 mb-2">
            <Button
              track-id="disable-track"
              variant="purple"
              :label="t('SQKII_VOUCHERS_BTN_GET_VOUVHERS')"
              @click="handleGetVouchers"
            />
            <Button
              track-id="disable-track"
              :label="t('SQKII_VOUCHERS_BTN_USE_VOUVHERS')"
              class="!w-full"
              @click="handleUseVouchers"
            />
          </div>
        </div>
        <Icon
          v-if="svUser"
          class="absolute -top-20 left-1/2 -translate-x-1/2 -z-10"
          :name="`/sov/vouchers/${BrandSponsors.Sqkii}`"
          :size="180"
        />
      </div>
    </div>

    <!-- Main Content -->
    <div
      class="py-5 transition-all duration-300 ease-out flex flex-col"
      :class="{
        'sticky top-0 h-full z-20': y >= height - SCROLL_THRESHOLD,
        'pt-20': y >= height - 40,
      }"
    >
      <!-- Sticky Balance Bar -->
      <div
        class="frame-balance-small hidden justify-between transform transition-all duration-300 ease-out"
        :class="{ '!flex': y >= height - SCROLL_THRESHOLD }"
        style="will-change: transform, opacity"
      >
        <div class="flex items-center gap-2">
          <Icon name="sv_balance" :size="48" />
          <div class="flex flex-col">
            <div
              class="text-base font-medium text-white opacity-70"
              v-html="t('SQKII_VOUCHERS_BALANCE_TITLE')"
            />
            <div class="text-2xl font-bold" v-html="balanceFormatted" />
          </div>
        </div>
        <div class="flex flex-col items-center">
          <Button track-id="disable-track" variant="secondary" shape="square" @click="scrollTop">
            <Icon name="arrow-up" />
          </Button>
          <div class="font-bold text-xs" v-html="t('SQKII_VOUCHERS_BACK_TO_TOP')" />
        </div>
      </div>

      <!-- Search and Info -->
      <div class="flex flex-col gap-5 mb-5 px-5">
        <div class="text-sm text-center" v-html="t('SQKII_VOUCHERS_BALANCE_DESC_1')" />
        <div class="relative">
          <Input
            v-model="searchInput"
            class="full-width"
            type="search"
            :label="t('SQKII_VOUCHERS_SEARCH')"
            :disabled="isLoadingOutlets"
          />
        </div>
      </div>

      <!-- Category Tabs -->
      <div
        v-if="!isLoadingOutlets && Object.keys(outlets).length"
        class="flex flex-nowrap items-center gap-2 overflow-x-auto snap-x snap-mandatory mb-5 mx-5 scroll-smooth"
      >
        <div
          v-for="category in categories"
          :key="category"
          style="background: linear-gradient(#b663e9, #4f46c1)"
          class="p-[1px] rounded-[10px]"
        >
          <div
            class="snap-start bg-[#200D37] rounded-[9px] px-5 py-3 w-max shrink-0 transition-colors"
            :class="{ '!bg-[#5D3AC0]': category === tab }"
            @click="tab = category"
            v-html="removeSpecialKey(category)"
          />
        </div>
      </div>

      <!-- Category Tabs Skeleton -->
      <div
        v-if="isLoadingOutlets"
        class="flex flex-nowrap items-center gap-2 overflow-x-auto snap-x snap-mandatory mb-5 mx-5 scroll-smooth"
      >
        <div
          v-for="i in 5"
          :key="`tab-skeleton-${i}`"
          class="snap-start bg-gray-600/30 rounded-[9px] px-5 py-3 w-20 h-12 shrink-0 shimmer animate-pulse"
        />
      </div>

      <!-- Merchant Acquisition Callout -->
      <div
        v-if="shouldShowCallout"
        class="card-alert relative mx-5 mb-5 py-2.5 px-4 text-sm italic"
        :class="[merchantAcquisition.last_type]"
      >
        <div
          class="text-sm pr-10"
          v-html="
            merchantAcquisition.last_type === 'friends'
              ? t('MERCHANT_ACQUISITION_FRIENDS')
              : t('MERCHANT_ACQUISITION_OWNERS')
          "
        />
        <Icon
          class="absolute right-2 top-2 cursor-pointer"
          name="cross"
          :size="12"
          @click="handleCloseCallout"
        />
      </div>

      <!-- Outlets Grid -->
      <div
        class="flex-1 min-h-0"
        :class="{
          'h-full': y >= height - SCROLL_THRESHOLD,
        }"
      >
        <div class="grid grid-cols-3 gap-4 overflow-y-auto px-5 scroll-smooth h-full pb-5">
          <!-- Loading Skeleton -->
          <template v-if="isLoadingOutlets">
            <div v-for="i in 12" :key="`skeleton-${i}`" class="animate-pulse">
              <div class="w-full aspect-square rounded-[10px] bg-gray-600/30 shimmer" />
              <div class="mt-[15px] h-4 bg-gray-600/30 rounded mx-auto w-3/4 shimmer" />
            </div>
          </template>

          <!-- Actual Outlets -->
          <template v-else-if="Object.keys(sortedOutlets).length">
            <div
              v-for="(outletList, merchantName) in sortedOutlets"
              :key="merchantName"
              @click="handleSelectMerchant(merchantName, outletList)"
            >
              <Icon
                :name="outletList[0]!.image"
                type="url"
                class="!w-full aspect-square border border-gray-600/30 rounded-2xl"
                lazy
              />
              <div class="text-center font-bold mt-[15px]" v-html="t(merchantName)" />
            </div>
          </template>

          <!-- No Results -->
          <template v-else>
            <div class="col-span-3 flex flex-col items-center justify-center py-16 text-center">
              <div
                class="text-gray-400 text-lg font-medium mb-2"
                v-html="t('SQKII_VOUCHERS_NO_RESULTS')"
              ></div>

              <div
                class="text-gray-500 text-sm"
                v-html="t('SQKII_VOUCHERS_TRY_DIFFERENT_SEARCH')"
              ></div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.banner {
  background: url('/imgs/sqkii-vouchers-bg.png') no-repeat;
  background-size: cover;
  width: 100%;
  height: 90.5vw;
}
.card-balance {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 96vw;
  height: 52vw;

  .frame-balance {
    padding: 10px 15px 20px;
    width: 100%;
    height: 100%;
    background-image: url('/imgs/sqkii-vouchers-balance.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 10;
  }
}

.card-alert {
  border-radius: 10px;
  background: #320b5b;
  &.owners {
    border: 2px solid #38cfe5;
    box-shadow: 0px 0px 7px 0px #11d1f9;
  }
  &.friends {
    border: 2px solid #fc5ac9;
    box-shadow: 0px 0px 7px 0px #fc78d6;
  }
}

.frame-balance-small {
  margin: 8px 8px 20px;
  width: calc(100% - 16px);
  height: auto;
  padding: 10px 25px;
  border-radius: 20px;
  border: 2px solid #b663e9;
  background: linear-gradient(180deg, #1b0d46 50.43%, #271364 109.13%);
  box-shadow:
    0px 4px 8px 2px rgba(73, 163, 191, 0.25),
    0px 4px 9px 6px rgba(159, 74, 211, 0.2);
}

.shimmer {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    animation: shimmer 1.5s infinite;
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}
</style>
