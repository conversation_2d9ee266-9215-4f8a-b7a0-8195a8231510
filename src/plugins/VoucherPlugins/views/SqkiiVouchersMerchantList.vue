<script lang="ts" setup>
import { computed } from 'vue';
import { SVOutlet } from '@types';

const OUTLET_TYPES = {
  THE_EYES_INC: 'The Eyes Inc',
} as const;

interface Props {
  merchant: string;
  outlets: SVOutlet[];
}

const props = defineProps<Props>();

const { t } = useI18n();
const { push } = useMicroRoute();

const merchantLocationText = computed(() =>
  t('MERCHANT_LOCATION_AVAILABLE', {
    NUMBER: props.outlets.length,
  }),
);

const tacHeaderText = computed(() =>
  t('SV_TAC_HEADER', {
    MERCHANT: props.merchant,
  }),
);

const tacDescriptionText = computed(() => {
  if (!props.outlets.length) return t('SV_TAC_DESC');

  const firstOutlet = props.outlets[0];

  if (!firstOutlet) return t('SV_TAC_DESC');

  if (firstOutlet.type === OUTLET_TYPES.THE_EYES_INC) return t('SV_TAC_DESC_TEI');

  if (firstOutlet?.min_spend && firstOutlet.min_spend > 0) {
    return t('SV_TAC_DESC_1', {
      AMOUNT: firstOutlet.min_spend,
      NAME: firstOutlet.type,
    });
  }

  return t('SV_TAC_DESC');
});

function handleOutletClick(outlet: SVOutlet): void {
  push('sqkii_vouchers_outlet_details', {
    outlet,
    merchant: props.merchant,
  });
}
</script>
<template>
  <div class="fullscreen bg-[#090422] flex flex-nowrap flex-col overflow-hidden">
    <!-- Header Section -->
    <div class="relative flex justify-center items-center w-full h-16 px-20 mb-5 shrink-0">
      <Button
        track-id="disable-track"
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-extrabold text-center w-full" v-html="merchant"></div>
    </div>

    <!-- Location Count -->
    <div class="px-6 text-sm mb-7" v-html="merchantLocationText"></div>

    <!-- Content Section -->
    <div class="flex flex-nowrap flex-col gap-4 overflow-y-auto px-6 pb-5">
      <!-- Terms and Conditions Notice -->
      <div class="bg-[#843EFF] rounded flex flex-nowrap gap-2 p-2.5">
        <Icon name="exclamation-mark" class="size-5" />
        <div class="flex flex-col gap-1">
          <div class="text-xs font-bold" v-html="tacHeaderText"></div>
          <div class="text-xs" v-html="tacDescriptionText"></div>
        </div>
      </div>
      <div
        v-for="outlet in outlets"
        :key="outlet.code"
        class="flex flex-nowrap justify-between items-center px-3 py-5 bg-[#5d3ac030] rounded-[10px]"
        @click="handleOutletClick(outlet)"
      >
        <div class="text-lg font-semibold" v-html="outlet.name"></div>
        <Icon name="arrow-left" class="!rotate-180" />
      </div>
    </div>
  </div>
</template>
