import { SettingModel } from '@services';
import { useGlobalStore } from '@stores';
import { TrackingPayload } from '@types';
import { last } from 'lodash';
import { getCurrentInstance } from 'vue';

type SessionType = 'page' | 'dialog';

interface PageSession {
  path: string;
  type: SessionType;
  enter_time: number;
  leave_time?: number;
  duration?: number;
  pause_time?: number;
}

const ACTIONS = {
  PAGE_ENTER: 'page_enter',
  PAGE_LEAVE: 'page_leave',
  DIALOG_ENTER: 'dialog_enter',
  DIALOG_LEAVE: 'dialog_leave',
  VISIT_LINK: 'visit_link',
} as const;

export function usePageTracker() {
  const storeGlobal = useGlobalStore();

  const { lastUserLocations } = storeToRefs(storeGlobal);

  const currentSessions = ref(new Map<string, PageSession>());

  function normalizedPath(path: string): string {
    const p = last(path.split('/'));
    if (!p) return '';
    return p.replace(/^\//, '');
  }

  function trackPageEnter(path: string, from?: string, to?: string): void {
    path = normalizedPath(path);

    const enter_time = Date.now();
    const session: PageSession = {
      path,
      type: 'page',
      enter_time: enter_time,
    };

    currentSessions.value.set(path, session);

    track({
      id: path,
      action: ACTIONS.PAGE_ENTER,
      data: {
        from: from ? normalizedPath(from) : undefined,
        to: to ? normalizedPath(to) : undefined,
        enter_time: new Date(enter_time).toISOString(),
      },
    });
  }

  function trackPageLeave(path: string, from?: string, to?: string): void {
    path = normalizedPath(path);
    const session = currentSessions.value.get(path);
    if (!session) return;

    const leave_time = Date.now();
    const duration = leave_time - session.enter_time;

    session.leave_time = leave_time;
    session.duration = duration;

    track({
      id: path,
      action: ACTIONS.PAGE_LEAVE,
      data: {
        from: from ? normalizedPath(from) : undefined,
        to: to ? normalizedPath(to) : undefined,
        enter_time: new Date(session.enter_time).toISOString(),
        leave_time: new Date(leave_time).toISOString(),
        duration,
      },
    });

    // Remove from active sessions
    currentSessions.value.delete(path);
  }

  function trackDialogEnter(path: string, from?: string, to?: string): void {
    path = normalizedPath(path);
    const enter_time = Date.now();
    const session: PageSession = {
      path,
      type: 'dialog',
      enter_time,
    };

    currentSessions.value.set(path, session);

    track({
      id: path,
      action: ACTIONS.DIALOG_ENTER,
      data: {
        from: from ? normalizedPath(from) : undefined,
        to: to ? normalizedPath(to) : undefined,
        enter_time: new Date(enter_time).toISOString(),
      },
    });
  }

  function trackDialogLeave(path: string, from?: string, to?: string): void {
    path = normalizedPath(path);
    const session = currentSessions.value.get(path);
    if (!session) return;

    const leave_time = Date.now();
    const duration = leave_time - session.enter_time;

    session.leave_time = leave_time;
    session.duration = duration;

    track({
      id: path,
      action: ACTIONS.DIALOG_LEAVE,
      data: {
        from: from ? normalizedPath(from) : undefined,
        to: to ? normalizedPath(to) : undefined,
        enter_time: new Date(session.enter_time).toISOString(),
        leave_time: new Date(leave_time).toISOString(),
        duration,
      },
    });

    currentSessions.value.delete(path);
  }

  function trackLinkClick(url: string): void {
    track({
      id: 'link_click',
      action: ACTIONS.VISIT_LINK,
      data: {
        url,
      },
    });
  }

  // Global function to setup automatic link tracking
  function setupGlobalLinkTracking(): void {
    const handleLinkClick = (event: Event): void => {
      const target = event.target as HTMLElement;
      const link = target.closest('a');

      if (link) {
        const href = link.href || link.getAttribute('href') || '';
        trackLinkClick(href);
      }
    };

    addEventListener('click', handleLinkClick, true);

    if (instance) {
      onBeforeUnmount(() => {
        removeEventListener('click', handleLinkClick, true);
      });
    }
  }

  // Clean up all active sessions on component unmount/page unload
  function cleanupAllSessions(): void {
    currentSessions.value.forEach((session, path) => {
      if (session.type === 'page') trackPageLeave(path);
      else trackDialogLeave(path);
    });
  }

  // Handle page visibility changes
  function handleVisibilityChange(): void {
    if (document.hidden) {
      // Page became hidden - track leave events for all active sessions
      currentSessions.value.forEach((session, path) => {
        if (session.type === 'page') trackPageLeave(path);
        else trackDialogLeave(path);
      });
    } else {
      // Page became visible - track enter events for sessions that were active
      const sessionsToRestore = new Map(currentSessions.value);
      sessionsToRestore.forEach((session, path) => {
        if (session.type === 'page') trackPageEnter(path);
        else trackDialogEnter(path);
      });
    }
  }

  function executeTracking(payload: TrackingPayload, additionalData?: Record<string, any>): void {
    try {
      const values = {
        id: payload.id,
        action: payload.action,
        data: {
          ...additionalData,
          ...payload.data,
        },
      };
      void SettingModel.trackEvent(values);
    } catch (error) {
      console.error('Tracking error:', error);
    }
  }

  function track(payload: TrackingPayload): void {
    executeTracking(payload);
  }

  function trackWithLocation(payload: TrackingPayload): void {
    const [lng, lat] = lastUserLocations.value;
    executeTracking(payload, { locations: { lng, lat } });
  }

  const instance = getCurrentInstance();
  if (instance) {
    onMounted(async () => {
      await nextTick();
      addEventListener('visibilitychange', handleVisibilityChange);
    });

    onBeforeUnmount(() => {
      cleanupAllSessions();
      removeEventListener('visibilitychange', handleVisibilityChange);
    });
  }

  return {
    trackPageEnter,
    trackPageLeave,
    trackDialogEnter,
    trackDialogLeave,
    cleanupAllSessions,
    track,
    trackWithLocation,
    trackLinkClick,
    setupGlobalLinkTracking,
  };
}
